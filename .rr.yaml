version: '3'
rpc:
    listen: 'tcp://127.0.0.1:6001'
http:
    address: '0.0.0.0:9001'
    middleware:
        - gzip
        - static
    static:
        dir: public
        forbid:
            - .php
            - .htaccess
    pool:
        num_workers: 1
        supervisor:
            max_worker_memory: 100
server:
    command: 'php app.php'
    relay: pipes
kv:
    local:
        driver: memory
        config:
            interval: 60

log:
    level: debug
    mode: development
    output: stderr
    encoding: console

#metrics:
#    address: '127.0.0.1:2112'
#temporal:
#    address: '127.0.0.1:7233'
