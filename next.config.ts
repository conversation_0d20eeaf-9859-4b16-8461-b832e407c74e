import type {NextConfig} from "next";

const nextConfig: NextConfig = {
    output: 'standalone',
    allowedDevOrigins: ["back-talk.ai","app.back-talk.ai", "localhost", "*************", "*************"],
    devIndicators: false,
    webpack(config) {
        // Remove any existing Next rules that might already match .svg
        config.module.rules = config.module.rules.map((rule:any) => {
            if (rule && typeof rule === 'object' && 'test' in rule && rule.test instanceof RegExp) {
                // If a rule already includes .svg, exclude it so our rule handles svgs
                if (rule.test.test('.svg')) {
                    return { ...rule, exclude: /\.svg$/i };
                }
            }
            return rule;
        });

        // One authoritative rule for all SVG imports
        config.module.rules.unshift({
            test: /\.svg$/i,
            oneOf: [
                // `import url from './icon.svg?url'` -> emits a file URL
                { resourceQuery: /url/, type: 'asset/resource' },
                // default: React component via SVGR
                {
                    use: [
                        {
                            loader: '@svgr/webpack',
                            options: {
                                svgo: true,
                                svgoConfig: { plugins: [{ name: 'removeViewBox', active: false }] },
                            },
                        },
                    ],
                },
            ],
        });

        console.log('[next.config] SVGR rule (catch-all) added');
        return config;
    },
};

export default nextConfig;
