openapi: 3.0.3
info:
  title: Back-Talk API
  description: |
    Back-Talk is an AI-powered conversation platform that helps businesses manage leads,
    conversations, and customer interactions through intelligent agents.

    ## Authentication
    This API uses JWT (JSON Web Tokens) for authentication. Include the token in the
    Authorization header as `Bearer <token>`.

    ## Base URLs
    - Production: `https://api.back-talk.ai`
    - Development: `http://localhost:9000/api`
  version: 1.0.0
  contact:
    name: Back-Talk Support
    url: https://back-talk.ai
  license:
    name: Proprietary

servers:
  - url: https://api.back-talk.ai
    description: Production server
  - url: http://localhost:9000/api
    description: Development server

security:
  - BearerAuth: []

paths:
  # Authentication Endpoints
  /auth/login_check:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with username/email and password, returns JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Username or email address
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  description: User password
                  example: password123
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Bad request - missing or invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Register a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - firstName
                - lastName
                - email
                - password
              properties:
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  minLength: 8
                  example: password123
      responses:
        '200':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '400':
          description: Bad request - validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConflictResponse'

  /auth/validate:
    post:
      tags:
        - Authentication
      summary: Validate JWT token
      description: Validate the current JWT token and return user information
      responses:
        '200':
          description: Token is valid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationResponse'
        '401':
          description: Token is invalid or expired
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh JWT token
      description: Refresh the current JWT token
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshResponse'
        '401':
          description: Unable to refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Logout user (client-side token removal)
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Logged out successfully

  # Agent Endpoints
  /api/v1/agent:
    get:
      tags:
        - Agents
      summary: List agents
      description: Get a list of agents for the authenticated user's organization
      parameters:
        - name: limit
          in: query
          description: Maximum number of agents to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          description: Number of agents to skip
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of agents
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Agent'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/agent/{agentId}:
    get:
      tags:
        - Agents
      summary: Get agent details
      description: Get detailed information about a specific agent
      parameters:
        - name: agentId
          in: path
          required: true
          description: Agent UUID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Agent details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '404':
          description: Agent not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Conversation Endpoints
  /conversation:
    get:
      tags:
        - Conversations
      summary: List conversations
      description: Get a list of conversations
      parameters:
        - name: limit
          in: query
          description: Maximum number of conversations to return
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: List of conversations
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Conversation'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from login endpoint

  schemas:
    # Authentication Schemas
    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        token:
          type: string
          description: JWT token for authentication
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        user:
          $ref: '#/components/schemas/User'
        expires_in:
          type: integer
          description: Token expiration time in seconds
          example: 604800

    RegisterResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Account created successfully. You can now log in.
        user_id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        requires_verification:
          type: boolean
          example: false
        needs_organization_setup:
          type: boolean
          example: true

    ValidationResponse:
      type: object
      properties:
        valid:
          type: boolean
          example: true
        user:
          $ref: '#/components/schemas/User'
        expires_at:
          type: integer
          description: Token expiration timestamp
          example: **********

    ValidationErrorResponse:
      type: object
      properties:
        valid:
          type: boolean
          example: false
        message:
          type: string
          example: Invalid or expired token

    RefreshResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        token:
          type: string
          description: New JWT token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        expires_in:
          type: integer
          description: Token expiration time in seconds
          example: 604800

    ConflictResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: email_exists
        message:
          type: string
          example: An account with this email address already exists.

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: Error message description
        error:
          type: string
          example: error_code

    # Entity Schemas
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        username:
          type: string
          example: <EMAIL>
        verified:
          type: boolean
          example: true
        person:
          $ref: '#/components/schemas/Person'

    Person:
      type: object
      properties:
        firstName:
          type: string
          nullable: true
          example: John
        lastName:
          type: string
          nullable: true
          example: Doe
        email:
          type: string
          nullable: true
          example: <EMAIL>

    Agent:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: Customer Service Agent
        status:
          type: string
          enum: [active, inactive, scheduled]
          example: active
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        organization:
          $ref: '#/components/schemas/Organization'
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    Conversation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        lead:
          $ref: '#/components/schemas/Lead'
        agent:
          $ref: '#/components/schemas/Agent'
        status:
          type: string
          enum: [active, pending, resolved, archived]
          example: active
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    Lead:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        person:
          $ref: '#/components/schemas/Person'
        organization:
          $ref: '#/components/schemas/Organization'
        service:
          type: string
          nullable: true
          example: Web Development
        status:
          type: string
          enum: [new, contacted, qualified, converted, lost]
          example: new
        priority:
          type: string
          enum: [high, medium, low]
          example: medium
        sourceAgent:
          $ref: '#/components/schemas/Agent'
        estimatedValue:
          type: number
          format: float
          nullable: true
          example: 5000.00
        actualValue:
          type: number
          format: float
          nullable: true
          example: 4500.00
        estimatedCost:
          type: number
          format: float
          nullable: true
          example: 2000.00
        lastContact:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-01T00:00:00Z
        score:
          type: integer
          nullable: true
          example: 85
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    Organization:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: Acme Corporation
        industry:
          $ref: '#/components/schemas/Industry'
        address:
          $ref: '#/components/schemas/Address'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    Industry:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: Technology
        description:
          type: string
          nullable: true
          example: Software and technology services

    Address:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        street1:
          type: string
          example: 123 Main St
        street2:
          type: string
          nullable: true
          example: Suite 100
        street3:
          type: string
          nullable: true
        street4:
          type: string
          nullable: true
        city:
          type: string
          example: New York
        state:
          type: string
          example: NY
        zip:
          type: string
          example: 10001
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    PhoneNumber:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        number:
          type: integer
          example: 5551234567
        type:
          type: string
          enum: [landline, virtual, mobile, fax]
          example: virtual
        status:
          type: string
          enum: [active, inactive, pending]
          example: active
        monthlyRate:
          type: number
          format: float
          nullable: true
          example: 29.99
        purchaseDate:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-01T00:00:00Z
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    Message:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        conversation:
          $ref: '#/components/schemas/Conversation'
        content:
          type: string
          example: Hello, how can I help you today?
        sender:
          type: string
          enum: [lead, agent, user]
          example: agent
        createdAt:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-02T00:00:00Z

    PaginationMeta:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 100
        count:
          type: integer
          description: Number of items in current page
          example: 20
        per_page:
          type: integer
          description: Items per page
          example: 20
        current_page:
          type: integer
          description: Current page number
          example: 1
        total_pages:
          type: integer
          description: Total number of pages
          example: 5
        has_more:
          type: boolean
          description: Whether there are more pages
          example: true

tags:
  - name: Authentication
    description: User authentication and authorization endpoints
  - name: Agents
    description: AI agent management endpoints
  - name: Conversations
    description: Conversation management endpoints
  - name: Leads
    description: Lead management endpoints
  - name: Organizations
    description: Organization management endpoints
