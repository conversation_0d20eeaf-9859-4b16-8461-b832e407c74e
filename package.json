{"name": "back-talk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 9000", "build": "next build", "start": "next start -p 9000", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.1", "@fortawesome/free-brands-svg-icons": "^7.0.1", "@fortawesome/free-regular-svg-icons": "^7.0.1", "@fortawesome/free-solid-svg-icons": "^7.0.1", "@fortawesome/react-fontawesome": "^3.0.0", "@radix-ui/themes": "^3.2.1", "class-variance-authority": "^0.7.1", "lucide-react": "^0.542.0", "next": "^15.5.1-canary.23", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.7"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "tailwindcss": "^4", "typescript": "^5"}}