<?php
namespace Database\Factory;

use App\Database\Entity\Organization;
use App\Database\Entity\Industry;
use App\Database\Entity\Address;
use App\Database\Entity\PhoneNumber;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class OrganizationFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Organization::class;
    }

    public function makeEntity(array $definition): Organization
    {
        $organization = new Organization();
        $organization->name = $definition['name'];

        // Set relationships if provided
        if (isset($definition['industry'])) {
            $organization->industry = $definition['industry'];
        }
        if (isset($definition['address'])) {
            $organization->address = $definition['address'];
        }
        if (isset($definition['phoneNumber'])) {
            $organization->phoneNumber = $definition['phoneNumber'];
        }

        return $organization;
    }

    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
        ];
    }

    public function withIndustry(Industry $industry): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['industry' => $industry]);
    }

    public function withAddress(Address $address): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['address' => $address]);
    }

    public function withPhoneNumber(PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['phoneNumber' => $phoneNumber]);
    }
}
