<?php
namespace Database\Factory;

use App\Database\Entity\ForwardSequence;
use App\Database\Entity\Agent;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class ForwardSequenceFactory extends AbstractFactory
{
    public function entity(): string
    {
        return ForwardSequence::class;
    }

    public function makeEntity(array $definition): ForwardSequence
    {
        $forwardSequence = new ForwardSequence();

        // Set relationships if provided
        if (isset($definition['agent'])) {
            $forwardSequence->agent = $definition['agent'];
        }

        return $forwardSequence;
    }

    public function definition(): array
    {
        return [
            // ForwardSequence doesn't have any direct properties besides relationships
        ];
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }
}
