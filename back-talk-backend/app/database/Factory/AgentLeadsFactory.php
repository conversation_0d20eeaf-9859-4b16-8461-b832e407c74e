<?php
namespace Database\Factory;

use App\Database\Entity\AgentLeads;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class AgentLeadsFactory extends AbstractFactory
{
    public function entity(): string
    {
        return AgentLeads::class;
    }

    public function makeEntity(array $definition): AgentLeads
    {
        $agentLeads = new AgentLeads();
        
        // AgentLeads is a pivot table with only timestamps and ID
        // The actual relationships are handled by Cycle ORM
        
        return $agentLeads;
    }

    public function definition(): array
    {
        return [
            // AgentLeads only has timestamps and ID, no additional properties
        ];
    }
}
