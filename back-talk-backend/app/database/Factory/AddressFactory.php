<?php
namespace Database\Factory;

use App\Database\Entity\Address;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class AddressFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Address::class;
    }

    public function makeEntity(array $definition): Address
    {
        $address = new Address();
        $address->street1 = $definition['street1'];
        $address->street2 = $definition['street2'] ?? null;
        $address->street3 = $definition['street3'] ?? null;
        $address->street4 = $definition['street4'] ?? null;
        $address->city = $definition['city'] ?? null;
        $address->state = $definition['state'] ?? null;
        $address->zip = $definition['zip'] ?? null;

        return $address;
    }

    public function definition(): array
    {
        return [
            'street1' => $this->faker->streetAddress(),
            'street2' => $this->faker->optional(0.3)->secondaryAddress(),
            'street3' => $this->faker->optional(0.1)->buildingNumber(),
            'street4' => $this->faker->optional(0.05)->word(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
        ];
    }
}
