<?php

namespace App\Service;

use App\Database\Comment;
use App\Database\Entity\User;
use App\Database\Post;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'comments')]
class CommentService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    )
    {}

    public function comment(Post $post, User $user, string $message): Comment
    {
        $comment = new Comment($message, $user, $post);
        $this->entityManager->persist($comment)->run();
        return $comment;
    }
}
