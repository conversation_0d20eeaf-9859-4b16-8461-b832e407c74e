<?xml version="1.0"?>
<psalm
    errorLevel="2"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    hoistConstants="true"
    findUnusedPsalmSuppress="false"
    findUnusedVariablesAndParams="true"
    findUnusedBaselineEntry="true"
    findUnusedCode="false"
    ensureArrayStringOffsetsExist="true"
    addParamDefaultToDocblockType="true"
    strictBinaryOperands="true"
    errorBaseline="psalm-baseline.xml"
>
    <projectFiles>
        <directory name="app/src"/>
        <ignoreFiles>
            <directory name="vendor"/>
        </ignoreFiles>
    </projectFiles>
</psalm>
