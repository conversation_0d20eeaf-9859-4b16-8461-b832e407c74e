# Environment (prod or local)
APP_ENV=local

# Debug mode set to TRUE disables view caching and enables higher verbosity
DEBUG=true

# Verbosity level
VERBOSITY_LEVEL=verbose # basic, verbose, debug

# Set to an application specific value, used to encrypt/decrypt cookies etc
ENCRYPTER_KEY={encrypt-key}

# Monolog
MONOLOG_DEFAULT_CHANNEL=default # Use "roadrunner" channel if you want to use RoadRunner logger
MONOLOG_DEFAULT_LEVEL=DEBUG # DEBUG, INFO, NOTICE, WARNING, ERROR, CRITICAL, ALERT, EMERGENCY

# Cache
CACHE_STORAGE=rr-local

# Storage
STORAGE_DEFAULT=default

# Telemetry
TELEMETRY_DRIVER=null

# Tokenizer
TOKENIZER_CACHE_TARGETS=false
TOKENIZER_LOAD_CLASSES=true
TOKENIZER_LOAD_ENUMS=true
TOKENIZER_LOAD_INTERFACES=true

# View component options
VIEW_CACHE=false

# Session
SESSION_LIFETIME=86400
SESSION_COOKIE=sid

# Authorization
AUTH_TOKEN_TRANSPORT=cookie
AUTH_TOKEN_STORAGE=session

# Mailer
MAILER_DSN=null
MAILER_QUEUE=local
MAILER_QUEUE_CONNECTION=null
MAILER_FROM="Spiral <<EMAIL>>"

# Set to TRUE to disable confirmation in `migrate` commands
SAFE_MIGRATIONS=true

# Database connection options
DB_CONNECTION=sqlite
DB_LOG_QUERY_PARAMETERS=false
DB_LOG_INTERPOLATED_QUERIES=false
DB_WITH_DATETIME_MICROSECONDS=false
DB_DATABASE=spiral
DB_HOST=127.0.0.1
DB_PORT=3307
DB_USERNAME=root
DB_PASSWORD=password

# Cycle Bridge (Don't forget to set `CYCLE_SCHEMA_CACHE` to `true` in production)
CYCLE_SCHEMA_CACHE=false
CYCLE_SCHEMA_WARMUP=false

# Temporal infrastructure configuration
ELASTICSEARCH_VERSION=8.18.0
POSTGRESQL_VERSION=15
TEMPORAL_VERSION=1.27.2
TEMPORAL_UI_VERSION=2.37.1

# Temporal bridge configuration
TEMPORAL_ADDRESS=127.0.0.1:7233
TEMPORAL_TASK_QUEUE=default
