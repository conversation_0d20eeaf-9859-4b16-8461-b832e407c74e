version: '3.8'

services:
  back-talk-ui:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: back-talk-ui
    ports:
      - "9000:9000"
    environment:
      - NODE_ENV=production
      - PORT=9000
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    networks:
      - back-talk-network
    # Optional: Add health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Optional: Add volume for logs
    volumes:
      - ./logs:/app/logs
    # Optional: Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  back-talk-network:
    driver: bridge

# Optional: Add volumes for persistent data
volumes:
  logs:
    driver: local
