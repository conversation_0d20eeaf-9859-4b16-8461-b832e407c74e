"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { But<PERSON> } from "@app/components/ui/button"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Switch } from "@app/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { Badge } from "@app/components/ui/badge"
import { Bot, Phone, ArrowLeft, Save, Plus, Trash2, Check, MapPin } from "lucide-react"
import Link from "next/link"
import { useSearchParams, useRouter } from "next/navigation"
import { useState } from "react"

export default function CreateAgentConfigurePage() {
    const searchParams = useSearchParams()
    const router = useRouter()
    const selectedNumber = searchParams.get("number")
    const numberId = searchParams.get("numberId")

    const [agentName, setAgentName] = useState("")
    const [forwardNumber, setForwardNumber] = useState("")
    const [forwardSequence, setForwardSequence] = useState([{ number: "", waitTime: 30 }])
    const [autoResponderEnabled, setAutoResponderEnabled] = useState(true)
    const [toneSample, setToneSample] = useState("")
    const [responseDelay, setResponseDelay] = useState("1-5")
    const [isCreating, setIsCreating] = useState(false)

    const responseDelayOptions = [
        { value: "0", label: "0 seconds" },
        { value: "1-30", label: "1 - 30 seconds" },
        { value: "30-60", label: "30 - 60 seconds" },
        { value: "1-5", label: "1 - 5 minutes" },
        { value: "5-10", label: "5 - 10 minutes" },
        { value: "10-30", label: "10 - 30 minutes" },
        { value: "random", label: "Random (1 - 15 min)" },
        { value: "30-60", label: "30 min - 1 hour" },
    ]

    const addForwardNumber = () => {
        setForwardSequence([...forwardSequence, { number: "", waitTime: 30 }])
    }

    const removeForwardNumber = (index: number) => {
        if (forwardSequence.length > 1) {
            setForwardSequence(forwardSequence.filter((_, i) => i !== index))
        }
    }

    const updateForwardSequence = (index: number, field: "number" | "waitTime", value: string | number) => {
        const updated = [...forwardSequence]
        updated[index] = { ...updated[index], [field]: value }
        setForwardSequence(updated)
    }

    const handleCreateAgent = async () => {
        setIsCreating(true)

        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000))

        // In a real app, you'd make an API call to create the agent
        console.log("Creating agent with:", {
            name: agentName,
            phone: selectedNumber,
            forwardNumber,
            forwardSequence,
            autoResponder: {
                enabled: autoResponderEnabled,
                toneSample,
                responseDelay,
            },
        })

        // Redirect to the new agent's details page
        router.push("/agents")
    }

    if (!selectedNumber) {
        return (
            <div className="space-y-6">
                <Card>
                    <CardContent className="text-center py-12">
                        <Phone className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No phone number selected</h3>
                        <p className="text-muted-foreground mb-4">Please go back and select a phone number first.</p>
                        <Button asChild>
                            <Link href="/agents/create">Select Phone Number</Link>
                        </Button>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button variant="ghost" size="sm" asChild>
                        <Link href="/agents/create">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Number Selection
                        </Link>
                    </Button>
                    <div className="flex items-center space-x-3">
                        <Avatar className="h-12 w-12">
                            <AvatarFallback className="bg-primary/10">
                                <Bot className="h-6 w-6 text-primary" />
                            </AvatarFallback>
                        </Avatar>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Configure New Agent</h1>
                            <p className="text-muted-foreground">Step 2: Set up your agent configuration</p>
                        </div>
                    </div>
                </div>
                <div className="flex space-x-2">
                    <Button variant="outline" asChild>
                        <Link href="/agents">Cancel</Link>
                    </Button>
                    <Button onClick={handleCreateAgent} disabled={!agentName.trim() || isCreating}>
                        {isCreating ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Creating...
                            </>
                        ) : (
                            <>
                                <Save className="mr-2 h-4 w-4" />
                                Create Agent
                            </>
                        )}
                    </Button>
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Configuration Form */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Selected Number Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Selected Phone Number</CardTitle>
                            <CardDescription>The phone number assigned to this agent</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
                                <Phone className="h-5 w-5 text-primary" />
                                <div>
                                    <p className="font-mono font-medium text-lg">{selectedNumber}</p>
                                    <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                                        <MapPin className="h-3 w-3" />
                                        <span>New York, NY</span>
                                    </div>
                                </div>
                                <Check className="h-5 w-5 text-green-600 ml-auto" />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Basic Configuration */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Configuration</CardTitle>
                            <CardDescription>Configure the basic settings for your agent</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Agent Name *</Label>
                                <Input
                                    id="name"
                                    placeholder="e.g., Main Business Line, Sales Inquiries, Customer Support"
                                    value={agentName}
                                    onChange={(e) => setAgentName(e.target.value)}
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Forward Configuration */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Call Forwarding</CardTitle>
                            <CardDescription>Configure how calls are forwarded to your phone numbers</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="forwardNumber">Primary Forward Number</Label>
                                <Input
                                    id="forwardNumber"
                                    placeholder="(*************"
                                    value={forwardNumber}
                                    onChange={(e) => setForwardNumber(e.target.value)}
                                />
                            </div>

                            <Separator />

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <Label>Forward Sequence (Optional)</Label>
                                    <Button size="sm" variant="outline" onClick={addForwardNumber}>
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Number
                                    </Button>
                                </div>

                                {forwardSequence.map((item, index) => (
                                    <div key={index} className="flex items-center space-x-2 p-3 border rounded-lg">
                                        <div className="flex-1">
                                            <Input
                                                value={item.number}
                                                onChange={(e) => updateForwardSequence(index, "number", e.target.value)}
                                                placeholder="Phone number"
                                            />
                                        </div>
                                        <div className="w-32">
                                            <Input
                                                type="number"
                                                value={item.waitTime}
                                                onChange={(e) =>
                                                    updateForwardSequence(index, "waitTime", Number.parseInt(e.target.value) || 30)
                                                }
                                                placeholder="Wait time"
                                                className="text-center"
                                            />
                                        </div>
                                        <span className="text-sm text-muted-foreground">seconds</span>
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => removeForwardNumber(index)}
                                            disabled={forwardSequence.length === 1}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Auto Responder Configuration */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Auto Responder</CardTitle>
                            <CardDescription>Configure how your agent responds to messages</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="autoResponderEnabled"
                                    checked={autoResponderEnabled}
                                    onCheckedChange={setAutoResponderEnabled}
                                />
                                <Label htmlFor="autoResponderEnabled">Enable Auto Responder</Label>
                            </div>

                            {autoResponderEnabled && (
                                <>
                                    <div className="space-y-2">
                                        <Label htmlFor="toneSample">Tone Sample</Label>
                                        <Textarea
                                            id="toneSample"
                                            value={toneSample}
                                            onChange={(e) => setToneSample(e.target.value)}
                                            placeholder="e.g., Professional and friendly, always offer to schedule a callback"
                                            rows={3}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Provide an example of how you want the agent to respond to give it the right tone
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="responseDelay">Response Delay</Label>
                                        <Select value={responseDelay} onValueChange={setResponseDelay}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {responseDelayOptions.map((option) => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <p className="text-xs text-muted-foreground">
                                            Time to wait before responding to simulate human behavior
                                        </p>
                                    </div>
                                </>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Configuration Tips */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Configuration Tips</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3 text-sm">
                            <div className="p-3 bg-muted rounded-lg">
                                <p className="font-medium mb-1">Agent Name</p>
                                <p className="text-muted-foreground">
                                    Choose a descriptive name that identifies the purpose of this agent
                                </p>
                            </div>
                            <div className="p-3 bg-muted rounded-lg">
                                <p className="font-medium mb-1">Response Delay</p>
                                <p className="text-muted-foreground">Adding a delay makes responses feel more natural and human-like</p>
                            </div>
                            <div className="p-3 bg-muted rounded-lg">
                                <p className="font-medium mb-1">Tone Sample</p>
                                <p className="text-muted-foreground">
                                    Be specific about personality, formality level, and key phrases to use
                                </p>
                            </div>
                            <div className="p-3 bg-muted rounded-lg">
                                <p className="font-medium mb-1">Forward Sequence</p>
                                <p className="text-muted-foreground">
                                    Numbers are tried in order with the specified wait time between attempts
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Next Steps</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3 text-sm">
                            <div className="flex items-start space-x-2">
                                <Badge variant="outline" className="mt-0.5">
                                    1
                                </Badge>
                                <p className="text-muted-foreground">Agent will be created and activated</p>
                            </div>
                            <div className="flex items-start space-x-2">
                                <Badge variant="outline" className="mt-0.5">
                                    2
                                </Badge>
                                <p className="text-muted-foreground">Phone number will be provisioned</p>
                            </div>
                            <div className="flex items-start space-x-2">
                                <Badge variant="outline" className="mt-0.5">
                                    3
                                </Badge>
                                <p className="text-muted-foreground">You can start receiving calls immediately</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
