"use client"

import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { But<PERSON> } from "@app/components/ui/button"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Badge } from "@app/components/ui/badge"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { ArrowLeft, Search, Phone, MapPin, ArrowRight, Loader2, Bot, Check } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useRouter } from "next/navigation"

interface AvailableNumber {
    id: string
    number: string
    areaCode: string
    city: string
    state: string
    monthlyRate: number
    setupFee: number
    features: string[]
}

export default function CreateAgentPage() {
    const router = useRouter()
    const [searchQuery, setSearchQuery] = useState("")
    const [isSearching, setIsSearching] = useState(false)
    const [availableNumbers, setAvailableNumbers] = useState<AvailableNumber[]>([])
    const [selectedNumber, setSelectedNumber] = useState<AvailableNumber | null>(null)
    const [hasSearched, setHasSearched] = useState(false)

    // Simulate API call to search for available numbers
    const searchNumbers = async () => {
        if (!searchQuery.trim()) return

        setIsSearching(true)
        setHasSearched(false)

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1500))

        // Mock available numbers based on search query
        const mockNumbers: AvailableNumber[] = [
            {
                id: "1",
                number: "(555) 201-3456",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
            {
                id: "2",
                number: "(555) 202-7890",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding", "Toll-Free"],
            },
            {
                id: "3",
                number: "(555) 203-1234",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 12.99,
                setupFee: 5.0,
                features: ["SMS", "Voicemail"],
            },
            {
                id: "4",
                number: "(555) 204-5678",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 18.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding", "Analytics"],
            },
            {
                id: "5",
                number: "(555) 205-9012",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
            {
                id: "6",
                number: "(555) 206-3456",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
            {
                id: "7",
                number: "(555) 207-7890",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 22.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding", "Premium Support"],
            },
            {
                id: "8",
                number: "(555) 208-1234",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
            {
                id: "9",
                number: "(555) 209-5678",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
            {
                id: "10",
                number: "(555) 210-9012",
                areaCode: "555",
                city: "New York",
                state: "NY",
                monthlyRate: 15.99,
                setupFee: 0,
                features: ["SMS", "Voicemail", "Call Forwarding"],
            },
        ]

        setAvailableNumbers(mockNumbers)
        setIsSearching(false)
        setHasSearched(true)
    }

    const handleContinue = () => {
        if (selectedNumber) {
            // In a real app, you'd pass the selected number data to the next step
            router.push(`/agents/create/configure?number=${selectedNumber.number}&numberId=${selectedNumber.id}`)
        }
    }

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === "Enter") {
            searchNumbers()
        }
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button variant="ghost" size="sm" asChild>
                        <Link href="/agents">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to Agents
                        </Link>
                    </Button>
                    <div className="flex items-center space-x-3">
                        <Avatar className="h-12 w-12">
                            <AvatarFallback className="bg-primary/10">
                                <Bot className="h-6 w-6 text-primary" />
                            </AvatarFallback>
                        </Avatar>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">Create New Agent</h1>
                            <p className="text-muted-foreground">Step 1: Select a phone number for your agent</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Search Section */}
            <Card>
                <CardHeader>
                    <CardTitle>Find Available Phone Numbers</CardTitle>
                    <CardDescription>
                        Search by location (city, state) or area code to find available phone numbers
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex space-x-2">
                        <div className="flex-1">
                            <Label htmlFor="search" className="sr-only">
                                Search location or area code
                            </Label>
                            <Input
                                id="search"
                                placeholder="Enter city, state, or area code (e.g., 'New York, NY' or '555')"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyPress={handleKeyPress}
                                disabled={isSearching}
                            />
                        </div>
                        <Button onClick={searchNumbers} disabled={isSearching || !searchQuery.trim()}>
                            {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                            {isSearching ? "Searching..." : "Search"}
                        </Button>
                    </div>

                    {isSearching && (
                        <div className="text-center py-8">
                            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                            <p className="text-muted-foreground">Searching for available numbers...</p>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Results Section */}
            {hasSearched && !isSearching && (
                <Card>
                    <CardHeader>
                        <CardTitle>Available Numbers</CardTitle>
                        <CardDescription>
                            {availableNumbers.length} numbers found for "{searchQuery}"
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            {availableNumbers.map((number) => (
                                <div
                                    key={number.id}
                                    className={`p-4 border rounded-lg cursor-pointer transition-all hover:border-primary/50 ${
                                        selectedNumber?.id === number.id
                                            ? "border-primary bg-primary/5 ring-1 ring-primary/20"
                                            : "border-border"
                                    }`}
                                    onClick={() => setSelectedNumber(number)}
                                >
                                    <div className="flex items-start justify-between">
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2">
                                                <Phone className="h-4 w-4 text-primary" />
                                                <span className="font-mono font-medium text-lg">{number.number}</span>
                                                {selectedNumber?.id === number.id && <Check className="h-4 w-4 text-primary" />}
                                            </div>
                                            <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                                                <MapPin className="h-3 w-3" />
                                                <span>
                          {number.city}, {number.state}
                        </span>
                                            </div>
                                            <div className="flex flex-wrap gap-1">
                                                {number.features.map((feature) => (
                                                    <Badge key={feature} variant="secondary" className="text-xs">
                                                        {feature}
                                                    </Badge>
                                                ))}
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">${number.monthlyRate}/mo</p>
                                            {number.setupFee > 0 && <p className="text-xs text-muted-foreground">${number.setupFee} setup</p>}
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Continue Button */}
            {selectedNumber && (
                <div className="flex justify-end">
                    <Button onClick={handleContinue} size="lg">
                        Continue with {selectedNumber.number}
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                </div>
            )}

            {/* Empty State */}
            {hasSearched && !isSearching && availableNumbers.length === 0 && (
                <Card>
                    <CardContent className="text-center py-12">
                        <Phone className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No numbers found</h3>
                        <p className="text-muted-foreground mb-4">
                            No available numbers found for "{searchQuery}". Try a different location or area code.
                        </p>
                        <Button variant="outline" onClick={() => setSearchQuery("")}>
                            Try Different Search
                        </Button>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}
