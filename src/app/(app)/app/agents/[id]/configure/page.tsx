"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Button } from "@app/components/ui/button"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Switch } from "@app/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { Bot, Phone, ArrowLeft, Save, Plus, Trash2, Clock, User, Calendar } from "lucide-react"
import Link from "next/link"
import { notFound } from "next/navigation"
import { useState, useEffect, use } from "react"
import Agent from "@app/types/agent";

interface AgentConfigurePageProps {
    params: Promise<{
        id: string
    }>;
}

export default function AgentConfigurePage(props: AgentConfigurePageProps) {
    const params = use(props.params);
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [agent, setAgent] = useState<Agent | null>(null)
    const [saving, setSaving] = useState(false)
    const [saveError, setSaveError] = useState<string | null>(null)

    // Form state
    const [formData, setFormData] = useState({
        name: '',
        status: '',
        primary_forward_number: '',
        auto_responder: {
            enabled: false,
            tone_sample: '',
            response_delay: '0'
        },
        forward_sequence: [] as Array<{
            number: string,
            wait_time: number
        }>
    })

    const fetchAgent = async () => {
        setLoading(true)
        setError(null)
        try {
            const response = await fetch(`https://api.back-talk.ai/api/v1/agent/${params.id}`)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            setAgent(data)

            // Initialize form data with agent data
            const forwardSequence = data.forwardSequence || []
            const primaryForward = forwardSequence.find((item: any) => item.waitTime === 0)
            const additionalForwards = forwardSequence.filter((item: any) => item.waitTime > 0)

            setFormData({
                name: data.name || '',
                status: data.status || '',
                primary_forward_number: primaryForward?.number || data.forwardNumber || '',
                auto_responder: {
                    enabled: data.autoResponder?.enabled || false,
                    tone_sample: data.autoResponder?.toneSample || '',
                    response_delay: data.autoResponder?.responseDelay || '0'
                },
                forward_sequence: additionalForwards.map((item: any) => ({
                    number: item.number || '',
                    wait_time: item.waitTime || 30
                }))
            })
        }
        catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch agent')
        } finally {
            setLoading(false)
        }
    }

    const saveAgent = async () => {
        if (!agent) return

        setSaving(true)
        setSaveError(null)

        try {
            const updateData: any = {}

            // Only include changed fields
            if (formData.name !== agent.name) {
                updateData.name = formData.name
            }

            if (formData.status !== agent.status) {
                updateData.status = formData.status
            }

            // Auto responder changes
            const currentAutoResponder = agent.autoResponder || { enabled: false, toneSample: '', responseDelay: '0' }
            if (formData.auto_responder.enabled !== currentAutoResponder.enabled ||
                formData.auto_responder.tone_sample !== currentAutoResponder.toneSample ||
                formData.auto_responder.response_delay !== currentAutoResponder.responseDelay) {
                updateData.auto_responder = formData.auto_responder
            }

            // Forward sequence changes - combine primary and additional forwards
            const currentForwardSequence = agent.forwardSequence || []
            const currentPrimary = currentForwardSequence.find((item: any) => item.waitTime === 0)
            const currentAdditional = currentForwardSequence.filter((item: any) => item.waitTime > 0)

            // Build complete forward sequence with primary first (0 delay) then additional
            const completeForwardSequence = []
            if (formData.primary_forward_number.trim()) {
                completeForwardSequence.push({
                    number: formData.primary_forward_number,
                    wait_time: 0
                })
            }
            completeForwardSequence.push(...formData.forward_sequence)

            // Check if forward sequence has changed
            const currentCompleteSequence = []
            if (currentPrimary) {
                currentCompleteSequence.push({
                    number: currentPrimary.number,
                    wait_time: 0
                })
            }
            currentCompleteSequence.push(...currentAdditional.map((item: any) => ({
                number: item.number,
                wait_time: item.waitTime
            })))

            const hasForwardSequenceChanges = JSON.stringify(completeForwardSequence) !==
                JSON.stringify(currentCompleteSequence)

            if (hasForwardSequenceChanges) {
                updateData.forward_sequence = completeForwardSequence.map((item) => ({
                    number: parseInt(item.number.replace(/\D/g, '')), // Remove non-digits
                    wait_time: item.wait_time,
                    type: 'virtual',
                    status: 'active'
                }))
            }

            // Only make request if there are changes
            if (Object.keys(updateData).length === 0) {
                setSaving(false)
                return
            }

            const response = await fetch(`https://api.back-talk.ai/api/v1/agent/${params.id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
            }

            const updatedAgent = await response.json()
            setAgent(updatedAgent.data)

            // Update form data with the response
            const updatedForwardSequence = updatedAgent.data.forwardSequence || []
            const updatedPrimaryForward = updatedForwardSequence.find((item: any) => item.waitTime === 0)
            const updatedAdditionalForwards = updatedForwardSequence.filter((item: any) => item.waitTime > 0)

            setFormData({
                name: updatedAgent.data.name || '',
                status: updatedAgent.data.status || '',
                primary_forward_number: updatedPrimaryForward?.number || updatedAgent.data.forwardNumber || '',
                auto_responder: {
                    enabled: updatedAgent.data.autoResponder?.enabled || false,
                    tone_sample: updatedAgent.data.autoResponder?.toneSample || '',
                    response_delay: updatedAgent.data.autoResponder?.responseDelay || '0'
                },
                forward_sequence: updatedAdditionalForwards.map((item: any) => ({
                    number: item.number || '',
                    wait_time: item.waitTime || 30
                }))
            })

        } catch (err) {
            setSaveError(err instanceof Error ? err.message : 'Failed to save agent')
        } finally {
            setSaving(false)
        }
    }
    useEffect(() => {
        fetchAgent();
    }, [])

    const addForwardNumber = () => {
        setFormData(prev => ({
            ...prev,
            forward_sequence: [...prev.forward_sequence, { number: '', wait_time: 30 }]
        }))
    }

    const removeForwardNumber = (index: number) => {
        setFormData(prev => ({
            ...prev,
            forward_sequence: prev.forward_sequence.filter((_, i) => i !== index)
        }))
    }

    const updateForwardNumber = (index: number, field: 'number' | 'wait_time', value: string | number) => {
        setFormData(prev => ({
            ...prev,
            forward_sequence: prev.forward_sequence.map((item, i) =>
                i === index ? { ...item, [field]: value } : item
            )
        }))
    }

    // const breadcrumbs = [
    //     { label: "Agents", href: "/agents" },
    //     { label: agent.name, href: `/agents/${agent.id}` },
    //     { label: "Configure" },
    // ]

    const responseDelayOptions = [
        { value: "0", label: "0 seconds" },
        { value: "1-30", label: "1 - 30 seconds" },
        { value: "30-60", label: "30 - 60 seconds" },
        { value: "60-300", label: "1 - 5 minutes" },
        { value: "3000-6000", label: "5 - 10 minutes" },
        { value: "6000-18000", label: "10 - 30 minutes" },
        { value: "18000-36000", label: "30 min - 1 hour" },
        { value: "random", label: "Random (1 - 15 min)" },
    ]

    const statusOptions = [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
        { value: "scheduled", label: "Scheduled" },
    ]

    return (
        <>
            {loading || !agent ? (
                <div>Loading...</div>
            ) : error ? (
                <div>Error: {error}</div>
            ) : agent ? (<div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href={`/agents/${agent.id}`}>
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Details
                            </Link>
                        </Button>
                        <div className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12">
                                <AvatarFallback className="bg-primary/10">
                                    <Bot className="h-6 w-6 text-primary" />
                                </AvatarFallback>
                            </Avatar>
                            <div>
                                <h1 className="text-3xl font-bold tracking-tight">Configure Agent</h1>
                                <div className="flex items-center space-x-2 text-muted-foreground">
                                    <span>{agent.name}</span>
                                    <Badge variant="outline">{agent.phone}</Badge>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="flex space-x-2">
                        <Button variant="outline" onClick={() => fetchAgent()}>Cancel</Button>
                        <Button onClick={saveAgent} disabled={saving}>
                            <Save className="mr-2 h-4 w-4" />
                            {saving ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </div>

                {/* Error Display */}
                {saveError && (
                    <div className="bg-destructive/15 border border-destructive/20 text-destructive px-4 py-3 rounded-lg">
                        <p className="text-sm font-medium">Error saving changes:</p>
                        <p className="text-sm">{saveError}</p>
                    </div>
                )}

                <div className="grid gap-6 lg:grid-cols-3">
                    {/* Configuration Form */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Basic Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Basic Configuration</CardTitle>
                                <CardDescription>Configure the basic settings for your agent</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="name">Agent Name</Label>
                                        <Input
                                            id="name"
                                            value={formData.name}
                                            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status</Label>
                                        <Select
                                            value={formData.status}
                                            onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {statusOptions.map((option) => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Forward Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Call Forwarding</CardTitle>
                                <CardDescription>Configure how calls are forwarded to your phone numbers</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="forwardNumber">Primary Forward Number</Label>
                                    <Input
                                        id="forwardNumber"
                                        value={formData.primary_forward_number}
                                        onChange={(e) => setFormData(prev => ({ ...prev, primary_forward_number: e.target.value }))}
                                        placeholder="(*************"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        This number will be called immediately (0 second delay) when a call comes in
                                    </p>
                                </div>

                                <Separator />

                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label>Additional Forward Numbers</Label>
                                            <p className="text-xs text-muted-foreground">
                                                These numbers will be called after the primary number with the specified delay
                                            </p>
                                        </div>
                                        <Button size="sm" variant="outline" onClick={addForwardNumber}>
                                            <Plus className="h-4 w-4 mr-2" />
                                            Add Number
                                        </Button>
                                    </div>

                                    {formData.forward_sequence.length > 0 ? (
                                        formData.forward_sequence.map((item, index) => (
                                            <div key={index} className="flex items-center space-x-2 p-3 border rounded-lg">
                                                <div className="flex-1">
                                                    <Input
                                                        value={item.number}
                                                        onChange={(e) => updateForwardNumber(index, 'number', e.target.value)}
                                                        placeholder="Phone number"
                                                    />
                                                </div>
                                                <div className="w-32">
                                                    <Input
                                                        type="number"
                                                        value={item.wait_time}
                                                        onChange={(e) => updateForwardNumber(index, 'wait_time', parseInt(e.target.value) || 0)}
                                                        placeholder="Wait time"
                                                        className="text-center"
                                                    />
                                                </div>
                                                <span className="text-sm text-muted-foreground">seconds</span>
                                                <Button size="sm" variant="ghost" onClick={() => removeForwardNumber(index)}>
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground">No additional forward numbers configured</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Auto Responder Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Auto Responder</CardTitle>
                                <CardDescription>Configure how your agent responds to messages</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="autoResponderEnabled"
                                        checked={formData.auto_responder.enabled}
                                        onCheckedChange={(checked) => setFormData(prev => ({
                                            ...prev,
                                            auto_responder: { ...prev.auto_responder, enabled: checked }
                                        }))}
                                    />
                                    <Label htmlFor="autoResponderEnabled">Enable Auto Responder</Label>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="toneSample">Tone Sample</Label>
                                    <Textarea
                                        id="toneSample"
                                        value={formData.auto_responder.tone_sample}
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            auto_responder: { ...prev.auto_responder, tone_sample: e.target.value }
                                        }))}
                                        placeholder="Describe the tone and style for responses..."
                                        rows={3}
                                    />
                                    <p className="text-xs text-muted-foreground">
                                        Provide an example of how you want the agent to respond to give it the right tone
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="responseDelay">Response Delay</Label>
                                    <Select
                                        value={formData.auto_responder.response_delay}
                                        onValueChange={(value) => setFormData(prev => ({
                                            ...prev,
                                            auto_responder: { ...prev.auto_responder, response_delay: value }
                                        }))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {responseDelayOptions.map((option) => (
                                                <SelectItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <p className="text-xs text-muted-foreground">
                                        Time to wait before responding to simulate human behavior
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Non-Configurable Information */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Agent Information</CardTitle>
                                <CardDescription>Read-only agent details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex items-center space-x-2">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Phone Number</p>
                                            <p className="text-sm text-muted-foreground">{agent.phone}</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="flex items-center space-x-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Created</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(agent.createdAt).toLocaleDateString()} at{" "}
                                                {new Date(agent.createdAt).toLocaleTimeString()}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <User className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Created By</p>
                                            <p className="text-sm text-muted-foreground">{agent.createdBy}</p>
                                        </div>
                                    </div>

                                    <Separator />

                                    <div className="flex items-center space-x-2">
                                        <Clock className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Last Updated</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(agent.updatedAt).toLocaleDateString()} at{" "}
                                                {new Date(agent.updatedAt).toLocaleTimeString()}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <User className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Updated By</p>
                                            <p className="text-sm text-muted-foreground">{agent.updatedBy}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Configuration Tips</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3 text-sm">
                                <div className="p-3 bg-muted rounded-lg">
                                    <p className="font-medium mb-1">Response Delay</p>
                                    <p className="text-muted-foreground">
                                        Adding a delay makes responses feel more natural and human-like
                                    </p>
                                </div>
                                <div className="p-3 bg-muted rounded-lg">
                                    <p className="font-medium mb-1">Tone Sample</p>
                                    <p className="text-muted-foreground">
                                        Be specific about personality, formality level, and key phrases to use
                                    </p>
                                </div>
                                <div className="p-3 bg-muted rounded-lg">
                                    <p className="font-medium mb-1">Call Forwarding</p>
                                    <p className="text-muted-foreground">
                                        Primary number is called immediately (0s delay), then additional numbers are tried in order with specified wait times
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>) : (
                <div>Failed to load agent...</div>
            )}
        </>
    )
}
