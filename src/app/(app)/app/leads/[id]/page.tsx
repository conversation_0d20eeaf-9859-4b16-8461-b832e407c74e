"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Button } from "@app/components/ui/button"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Phone, Calendar, MessageSquare, Voicemail, Edit, Save, X, ArrowLeft, Bot, PhoneCall } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"
import Link from "next/link"
import { useState, use } from "react";

interface LeadDetailsPageProps {
    params: Promise<{
        id: string
    }>;
}

export default function LeadDetailsPage(props: LeadDetailsPageProps) {
    const params = use(props.params);
    const [isEditing, setIsEditing] = useState(false)
    const [editedLead, setEditedLead] = useState({
        name: "<PERSON>",
        email: "<EMAIL>",
        notes:
            "Kitchen sink leak, urgent repair needed. Customer mentioned they've had this issue for 2 weeks and it's getting worse.",
        status: "new",
        priority: "high",
        address: "123 Main St, Anytown, ST 12345",
    })

    // Mock lead data - in real app this would come from API
    const lead = {
        id: params.id,
        name: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "(*************",
        service: "Plumbing repair",
        status: "new",
        priority: "high",
        address: "123 Main St, Anytown, ST 12345",
        estimatedValue: "$850",
        actualValue: "$0",
        createdAt: "2024-03-15T10:30:00Z",
        updatedAt: "2024-03-15T14:20:00Z",
        updatedBy: "AI Agent",
        lastContact: "2024-03-15T14:20:00Z",
        notes:
            "Kitchen sink leak, urgent repair needed. Customer mentioned they've had this issue for 2 weeks and it's getting worse.",
        score: 85,
        // Metrics
        messageCount: 12,
        callCount: 3,
        voicemailCount: 1,
        appointmentCount: 1,
        cost: "$45.20",
        averageResponseTime: "2.5 minutes",
        sentiment: "Positive - Customer is eager to get repair done",
        aiSummary:
            "High-priority plumbing lead with confirmed budget and urgency. Customer has been dealing with kitchen sink leak for 2 weeks and needs immediate repair. Shows strong buying intent and has flexible scheduling.",
    }

    // Mock related data
    const conversations = [
        {
            id: "1",
            agent: "Main Business Line",
            lastMessage: "I can schedule someone for tomorrow morning if that works?",
            timestamp: "2024-03-15T14:20:00Z",
            messageCount: 8,
            status: "active",
        },
        {
            id: "2",
            agent: "Emergency Line",
            lastMessage: "Thank you for calling back. The leak is getting worse.",
            timestamp: "2024-03-15T12:15:00Z",
            messageCount: 4,
            status: "resolved",
        },
    ]

    const calls = [
        {
            id: "1",
            type: "incoming",
            duration: "4:32",
            timestamp: "2024-03-15T10:30:00Z",
            agent: "Main Business Line",
            outcome: "Lead created",
        },
        {
            id: "2",
            type: "outgoing",
            duration: "2:15",
            timestamp: "2024-03-15T13:45:00Z",
            agent: "Sales Team",
            outcome: "Follow-up scheduled",
        },
    ]

    const appointments = [
        {
            id: "1",
            title: "Kitchen Sink Repair Assessment",
            date: "2024-03-16T09:00:00Z",
            duration: "1 hour",
            status: "scheduled",
            agent: "Main Business Line",
        },
    ]

    const agents = [
        {
            id: "1",
            name: "Main Business Line",
            phone: "(*************",
            interactions: 8,
        },
        {
            id: "2",
            name: "Emergency Line",
            phone: "(*************",
            interactions: 4,
        },
    ]

    const handleSave = () => {
        // In real app, this would make API call to update lead
        console.log("Saving lead updates:", editedLead)
        setIsEditing(false)
    }

    const handleCancel = () => {
        // Reset to original values
        setEditedLead({
            name: lead.name,
            email: lead.email,
            notes: lead.notes,
            status: lead.status,
            priority: lead.priority,
            address: lead.address,
        })
        setIsEditing(false)
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case "new":
                return "default"
            case "contacted":
                return "secondary"
            case "qualified":
                return "outline"
            case "converted":
                return "default"
            case "lost":
                return "destructive"
            default:
                return "secondary"
        }
    }

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case "high":
                return "text-red-500"
            case "medium":
                return "text-yellow-500"
            case "low":
                return "text-green-500"
            default:
                return "text-gray-500"
        }
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Link href="/leads">
                        <Button variant="ghost" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Leads
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{lead.name}</h1>
                        <p className="text-muted-foreground">
                            {lead.service} • {lead.phone}
                        </p>
                    </div>
                </div>
                <div className="flex gap-2">
                    {isEditing ? (
                        <>
                            <Button variant="outline" onClick={handleCancel}>
                                <X className="mr-2 h-4 w-4" />
                                Cancel
                            </Button>
                            <Button onClick={handleSave}>
                                <Save className="mr-2 h-4 w-4" />
                                Save Changes
                            </Button>
                        </>
                    ) : (
                        <Button onClick={() => setIsEditing(true)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Lead
                        </Button>
                    )}
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Lead Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Lead Information</CardTitle>
                            <CardDescription>Contact details and current status</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="name">Name</Label>
                                    {isEditing ? (
                                        <Input
                                            id="name"
                                            value={editedLead.name}
                                            onChange={(e) => setEditedLead({ ...editedLead, name: e.target.value })}
                                        />
                                    ) : (
                                        <p className="text-sm font-medium">{lead.name}</p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="email">Email</Label>
                                    {isEditing ? (
                                        <Input
                                            id="email"
                                            type="email"
                                            value={editedLead.email}
                                            onChange={(e) => setEditedLead({ ...editedLead, email: e.target.value })}
                                        />
                                    ) : (
                                        <p className="text-sm">{lead.email}</p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label>Phone</Label>
                                    <p className="text-sm">{lead.phone}</p>
                                </div>
                                <div className="space-y-2">
                                    <Label>Service Requested</Label>
                                    <p className="text-sm">{lead.service}</p>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="status">Status</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editedLead.status}
                                            onValueChange={(value) => setEditedLead({ ...editedLead, status: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="new">New</SelectItem>
                                                <SelectItem value="contacted">Contacted</SelectItem>
                                                <SelectItem value="qualified">Qualified</SelectItem>
                                                <SelectItem value="converted">Converted</SelectItem>
                                                <SelectItem value="lost">Lost</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <Badge variant={getStatusColor(lead.status)} className="capitalize w-fit">
                                            {lead.status}
                                        </Badge>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="priority">Priority</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editedLead.priority}
                                            onValueChange={(value) => setEditedLead({ ...editedLead, priority: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="high">High</SelectItem>
                                                <SelectItem value="medium">Medium</SelectItem>
                                                <SelectItem value="low">Low</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <span className={`text-sm font-medium ${getPriorityColor(lead.priority)} capitalize`}>
                      {lead.priority}
                    </span>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                {isEditing ? (
                                    <Input
                                        id="address"
                                        value={editedLead.address}
                                        onChange={(e) => setEditedLead({ ...editedLead, address: e.target.value })}
                                    />
                                ) : (
                                    <p className="text-sm">{lead.address}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Notes</Label>
                                {isEditing ? (
                                    <Textarea
                                        id="notes"
                                        value={editedLead.notes}
                                        onChange={(e) => setEditedLead({ ...editedLead, notes: e.target.value })}
                                        rows={3}
                                    />
                                ) : (
                                    <p className="text-sm">{lead.notes}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* AI Analysis */}
                    <Card>
                        <CardHeader>
                            <CardTitle>AI Analysis</CardTitle>
                            <CardDescription>Automated insights and sentiment analysis</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label>Lead Summary</Label>
                                <p className="text-sm mt-1">{lead.aiSummary}</p>
                            </div>
                            <div>
                                <Label>Sentiment Analysis</Label>
                                <p className="text-sm mt-1">{lead.sentiment}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Conversations */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Conversations</CardTitle>
                            <CardDescription>Latest interactions with this lead</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {conversations.map((conversation) => (
                                    <div key={conversation.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback>
                                                <Bot className="h-4 w-4" />
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium">{conversation.agent}</p>
                                                <Badge variant={conversation.status === "active" ? "default" : "secondary"} className="text-xs">
                                                    {conversation.status}
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-muted-foreground mt-1">{conversation.lastMessage}</p>
                                            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                                <span>{conversation.messageCount} messages</span>
                                                <span>{new Date(conversation.timestamp).toLocaleString()}</span>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Calls & Voicemails */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Call History</CardTitle>
                            <CardDescription>Phone interactions with this lead</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {calls.map((call) => (
                                    <div key={call.id} className="flex items-start gap-3 p-3 border rounded-lg">
                                        <div
                                            className={`p-2 rounded-full ${call.type === "incoming" ? "bg-green-100 text-green-600" : "bg-blue-100 text-blue-600"}`}
                                        >
                                            <PhoneCall className="h-4 w-4" />
                                        </div>
                                        <div className="flex-1">
                                            <div className="flex items-center justify-between">
                                                <p className="text-sm font-medium capitalize">{call.type} Call</p>
                                                <span className="text-xs text-muted-foreground">{call.duration}</span>
                                            </div>
                                            <p className="text-sm text-muted-foreground">{call.agent}</p>
                                            <p className="text-sm mt-1">{call.outcome}</p>
                                            <p className="text-xs text-muted-foreground mt-1">{new Date(call.timestamp).toLocaleString()}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Metrics */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Lead Metrics</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="text-center">
                                    <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                    <p className="text-2xl font-bold">{lead.messageCount}</p>
                                    <p className="text-xs text-muted-foreground">Messages</p>
                                </div>
                                <div className="text-center">
                                    <Phone className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                    <p className="text-2xl font-bold">{lead.callCount}</p>
                                    <p className="text-xs text-muted-foreground">Calls</p>
                                </div>
                                <div className="text-center">
                                    <Voicemail className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                    <p className="text-2xl font-bold">{lead.voicemailCount}</p>
                                    <p className="text-xs text-muted-foreground">Voicemails</p>
                                </div>
                                <div className="text-center">
                                    <Calendar className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                                    <p className="text-2xl font-bold">{lead.appointmentCount}</p>
                                    <p className="text-xs text-muted-foreground">Appointments</p>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Lead Score</span>
                                    <span className="text-sm font-medium">{lead.score}/100</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Estimated Value</span>
                                    <span className="text-sm font-medium">{lead.estimatedValue}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Cost</span>
                                    <span className="text-sm font-medium">{lead.cost}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Response</span>
                                    <span className="text-sm font-medium">{lead.averageResponseTime}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Last Contact</span>
                                    <span className="text-sm font-medium">{new Date(lead.lastContact).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Appointments */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {appointments.map((appointment) => (
                                    <div key={appointment.id} className="p-3 border rounded-lg">
                                        <div className="flex items-center justify-between mb-2">
                                            <p className="text-sm font-medium">{appointment.title}</p>
                                            <Badge variant="outline" className="text-xs">
                                                {appointment.status}
                                            </Badge>
                                        </div>
                                        <p className="text-xs text-muted-foreground">{appointment.agent}</p>
                                        <p className="text-xs text-muted-foreground">
                                            {new Date(appointment.date).toLocaleString()} • {appointment.duration}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Associated Agents */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Associated Agents</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {agents.map((agent) => (
                                    <div key={agent.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="text-sm font-medium">{agent.name}</p>
                                            <p className="text-xs text-muted-foreground">{agent.phone}</p>
                                        </div>
                                        <Badge variant="secondary" className="text-xs">
                                            {agent.interactions} interactions
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* System Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle>System Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3 text-sm">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Created</span>
                                <span>{new Date(lead.createdAt).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Updated</span>
                                <span>{new Date(lead.updatedAt).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Updated By</span>
                                <span>{lead.updatedBy}</span>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
