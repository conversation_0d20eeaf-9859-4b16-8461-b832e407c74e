import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"
import { Input } from "@app/components/ui/input"
import Link from "next/link"
import {
  Users,
  Search,
  Phone,
  Mail,
  Calendar,
  MoreHorizontal,
  Download,
  Plus,
  TrendingUp,
  Target,
  CheckCircle,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@app/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@app/components/ui/table"

export default function LeadsPage() {
  const breadcrumbs = [{ label: "Leads" }]

  // Mock data for leads
  const leads = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "(*************",
      service: "Plumbing repair",
      status: "new",
      priority: "high",
      source: "Main Business Line",
      estimatedValue: "$850",
      createdAt: "2024-03-15T10:30:00Z",
      lastContact: "2024-03-15T10:30:00Z",
      notes: "Kitchen sink leak, urgent repair needed",
      score: 85,
    },
    {
      id: "2",
      name: "Mike Chen",
      email: "<EMAIL>",
      phone: "(*************",
      service: "HVAC maintenance",
      status: "contacted",
      priority: "medium",
      source: "Emergency Line",
      estimatedValue: "$1,200",
      createdAt: "2024-03-15T09:15:00Z",
      lastContact: "2024-03-15T11:45:00Z",
      notes: "Annual maintenance contract opportunity",
      score: 72,
    },
    {
      id: "3",
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "(*************",
      service: "Kitchen renovation",
      status: "qualified",
      priority: "high",
      source: "Sales Inquiries",
      estimatedValue: "$15,000",
      createdAt: "2024-03-15T08:45:00Z",
      lastContact: "2024-03-15T14:20:00Z",
      notes: "Full kitchen remodel, budget confirmed",
      score: 92,
    },
    {
      id: "4",
      name: "Robert Brown",
      email: "<EMAIL>",
      phone: "(*************",
      service: "Roof inspection",
      status: "converted",
      priority: "medium",
      source: "Customer Support",
      estimatedValue: "$2,500",
      createdAt: "2024-03-14T16:20:00Z",
      lastContact: "2024-03-15T09:00:00Z",
      notes: "Scheduled for next week, deposit received",
      score: 95,
    },
    {
      id: "5",
      name: "Lisa Wilson",
      email: "<EMAIL>",
      phone: "(*************",
      service: "Bathroom repair",
      status: "lost",
      priority: "low",
      source: "Main Business Line",
      estimatedValue: "$600",
      createdAt: "2024-03-13T14:10:00Z",
      lastContact: "2024-03-14T10:30:00Z",
      notes: "Chose competitor, price sensitive",
      score: 45,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "default"
      case "contacted":
        return "secondary"
      case "qualified":
        return "outline"
      case "converted":
        return "default"
      case "lost":
        return "destructive"
      default:
        return "secondary"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-500"
      case "medium":
        return "text-yellow-500"
      case "low":
        return "text-green-500"
      default:
        return "text-gray-500"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500"
    if (score >= 60) return "text-yellow-500"
    return "text-red-500"
  }

  const stats = [
    {
      title: "Total Leads",
      value: leads.length.toString(),
      description: "All time",
      icon: Users,
    },
    {
      title: "New Leads",
      value: leads.filter((l) => l.status === "new").length.toString(),
      description: "This week",
      icon: Plus,
    },
    {
      title: "Conversion Rate",
      value: `${Math.round((leads.filter((l) => l.status === "converted").length / leads.length) * 100)}%`,
      description: "This month",
      icon: TrendingUp,
    },
    {
      title: "Pipeline Value",
      value: "$19,150",
      description: "Active leads",
      icon: Target,
    },
  ]

  return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Leads</h1>
            <p className="text-muted-foreground">Manage leads generated from your auto-responder conversations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Lead
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">{stat.title}</CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">{stat.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters and Search */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search leads..." className="pl-9" />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="contacted">Contacted</SelectItem>
              <SelectItem value="qualified">Qualified</SelectItem>
              <SelectItem value="converted">Converted</SelectItem>
              <SelectItem value="lost">Lost</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all-sources">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by source" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-sources">All Sources</SelectItem>
              <SelectItem value="main">Main Business Line</SelectItem>
              <SelectItem value="emergency">Emergency Line</SelectItem>
              <SelectItem value="sales">Sales Inquiries</SelectItem>
              <SelectItem value="support">Customer Support</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Leads Table */}
        <Card>
          <CardHeader>
            <CardTitle>All Leads</CardTitle>
            <CardDescription>Complete list of leads from your agents</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Lead</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Service</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Score</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {leads.map((lead) => (
                  <TableRow key={lead.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <AvatarInitials name={lead.name} />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Link href={`/leads/${lead.id}`} className="font-medium hover:text-primary-300 hover:underline">
                              {lead.name}
                          </Link>
                          <p className="text-xs text-muted-foreground line-clamp-1">{lead.notes}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1 text-sm">
                          <Phone className="h-3 w-3" />
                          {lead.phone}
                        </div>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Mail className="h-3 w-3" />
                          {lead.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{lead.service}</p>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusColor(lead.status)} className="capitalize">
                        {lead.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={`text-sm font-medium ${getPriorityColor(lead.priority)}`}>{lead.priority}</span>
                    </TableCell>
                    <TableCell>
                      <span className={`text-sm font-medium ${getScoreColor(lead.score)}`}>{lead.score}</span>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm font-medium">{lead.estimatedValue}</p>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm text-muted-foreground">{lead.source}</p>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm text-muted-foreground">{new Date(lead.createdAt).toLocaleDateString()}</p>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Phone className="mr-2 h-4 w-4" />
                            Call Lead
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Calendar className="mr-2 h-4 w-4" />
                            Schedule Meeting
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Mark as Converted
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Empty State */}
        {leads.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No leads yet</h3>
              <p className="text-muted-foreground">
                Leads will appear here when your agents start converting conversations into business opportunities
              </p>
            </CardContent>
          </Card>
        )}
      </div>
  )
}
