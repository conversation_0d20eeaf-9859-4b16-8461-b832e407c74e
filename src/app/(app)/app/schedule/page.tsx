"use client"

import { useState } from "react"
import Link from "next/link"
import { Calendar, Clock, User, Phone, MapPin, Plus, ChevronLeft, ChevronRight, Filter } from "lucide-react"
import { But<PERSON> } from "@app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"

// Mock appointment data
const mockAppointments = [
    {
        id: "1",
        leadName: "<PERSON>",
        leadPhone: "(*************",
        agentName: "Downtown Auto Sales",
        service: "Vehicle Inspection",
        date: "2025-09-15",
        time: "10:00 AM",
        status: "confirmed",
        location: "123 Main St, Downtown",
        notes: "Interested in 2023 Honda Civic",
    },
    {
        id: "2",
        leadName: "<PERSON>",
        leadPhone: "(*************",
        agentName: "Premium Motors",
        service: "Test Drive",
        date: "2025-10-15",
        time: "2:30 PM",
        status: "pending",
        location: "456 Oak Ave, Midtown",
        notes: "Looking for SUV options",
    },
    {
        id: "3",
        leadName: "Emily Rodriguez",
        leadPhone: "(*************",
        agentName: "City Car Center",
        service: "Financing Discussion",
        date: "2025-09-16",
        time: "11:15 AM",
        status: "confirmed",
        location: "789 Pine St, Uptown",
        notes: "First-time buyer, needs guidance",
    },
    {
        id: "4",
        leadName: "David Wilson",
        leadPhone: "(*************",
        agentName: "Elite Auto Group",
        service: "Trade-in Evaluation",
        date: "2025-09-04",
        time: "3:45 PM",
        status: "rescheduled",
        location: "321 Elm St, Westside",
        notes: "Has 2019 Toyota Camry to trade",
    },
]

const statusColors = {
    confirmed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
    rescheduled: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
}

export default function AppointmentsPage() {
    const [currentDate, setCurrentDate] = useState(new Date())
    const [view, setView] = useState<"calendar" | "list">("calendar")
    const [statusFilter, setStatusFilter] = useState<string>("all")

    // Generate calendar days
    const generateCalendarDays = () => {
        const year = currentDate.getFullYear()
        const month = currentDate.getMonth()
        const firstDay = new Date(year, month, 1)
        const lastDay = new Date(year, month + 1, 0)
        const startDate = new Date(firstDay)
        startDate.setDate(startDate.getDate() - firstDay.getDay())

        const days = []
        const current = new Date(startDate)

        for (let i = 0; i < 42; i++) {
            days.push(new Date(current))
            current.setDate(current.getDate() + 1)
        }

        return days
    }

    const getAppointmentsForDate = (date: Date) => {
        const dateStr = date.toISOString().split("T")[0]
        return mockAppointments.filter((apt) => apt.date === dateStr)
    }

    const filteredAppointments =
        statusFilter === "all" ? mockAppointments : mockAppointments.filter((apt) => apt.status === statusFilter)

    const calendarDays = generateCalendarDays()
    const monthNames = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ]

    return (
        <div className="flex-1 space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Appointments</h1>
                    <p className="text-muted-foreground">Manage and view scheduled appointments with leads</p>
                </div>
                <div className="flex items-center gap-4">
                    <Select value={view} onValueChange={(value: "calendar" | "list") => setView(value)}>
                        <SelectTrigger className="w-32">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="calendar">Calendar</SelectItem>
                            <SelectItem value="list">List</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button asChild>
                        <Link href="/schedule/create">
                            <Plus className="mr-2 h-4 w-4" />
                            New Appointment
                        </Link>
                    </Button>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">2</div>
                        <p className="text-xs text-muted-foreground">+1 from yesterday</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">This Week</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">8</div>
                        <p className="text-xs text-muted-foreground">+2 from last week</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Confirmed</CardTitle>
                        <User className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">6</div>
                        <p className="text-xs text-muted-foreground">75% confirmation rate</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">2</div>
                        <p className="text-xs text-muted-foreground">Need confirmation</p>
                    </CardContent>
                </Card>
            </div>

            {view === "calendar" ? (
                /* Calendar View */
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                            </CardTitle>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))}
                                >
                                    <ChevronLeft className="h-4 w-4" />
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
                                    Today
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))}
                                >
                                    <ChevronRight className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-7 gap-1 mb-4">
                            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                                <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                                    {day}
                                </div>
                            ))}
                        </div>
                        <div className="grid grid-cols-7 gap-1">
                            {calendarDays.map((day, index) => {
                                const appointments = getAppointmentsForDate(day)
                                const isCurrentMonth = day.getMonth() === currentDate.getMonth()
                                const isToday = day.toDateString() === new Date().toDateString()

                                return (
                                    <div
                                        key={index}
                                        className={`min-h-[100px] p-2 border rounded-lg ${
                                            isCurrentMonth ? "bg-background" : "bg-muted/30"
                                        } ${isToday ? "ring-2 ring-primary-300" : ""}`}
                                    >
                                        <div
                                            className={`text-sm font-medium mb-1 ${
                                                isCurrentMonth ? "text-foreground" : "text-muted-foreground"
                                            }`}
                                        >
                                            {day.getDate()}
                                        </div>
                                        <div className="space-y-1">
                                            {appointments.slice(0, 2).map((apt) => (
                                                <Link href={`/schedule/${apt.id}`} key={apt.id}>
                                                    <div
                                                        key={apt.id}
                                                        className="text-xs p-1 rounded bg-primary-800 text-primary truncate cursor-pointer hover:bg-primary-500"
                                                        title={`${apt.time} - ${apt.leadName}`}
                                                    >
                                                        {apt.time} {apt.leadName}
                                                    </div>
                                                </Link>
                                            ))}
                                            {appointments.length > 2 && (
                                                <div className="text-xs text-muted-foreground">+{appointments.length - 2} more</div>
                                            )}
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </CardContent>
                </Card>
            ) : (
                /* List View */
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle>Upcoming Appointments</CardTitle>
                            <div className="flex items-center gap-4">
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger className="w-40">
                                        <Filter className="mr-2 h-4 w-4" />
                                        <SelectValue placeholder="Filter by status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="confirmed">Confirmed</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="rescheduled">Rescheduled</SelectItem>
                                        <SelectItem value="cancelled">Cancelled</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {filteredAppointments.map((appointment) => (
                                <Card key={appointment.id} className="p-4">
                                    <div className="flex items-start justify-between">
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-3">
                                                <h3 className="font-semibold">{appointment.leadName}</h3>
                                                <Badge className={statusColors[appointment.status as keyof typeof statusColors]}>
                                                    {appointment.status}
                                                </Badge>
                                            </div>
                                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-4 w-4" />
                                                    {appointment.date}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <Clock className="h-4 w-4" />
                                                    {appointment.time}
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <Phone className="h-4 w-4" />
                                                    {appointment.leadPhone}
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-4 text-sm">
                                                <div className="flex items-center gap-1">
                                                    <User className="h-4 w-4" />
                                                    <span className="font-medium">{appointment.agentName}</span>
                                                </div>
                                                <span>•</span>
                                                <span>{appointment.service}</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                                <MapPin className="h-4 w-4" />
                                                {appointment.location}
                                            </div>
                                            {appointment.notes && (
                                                <p className="text-sm text-muted-foreground italic">"{appointment.notes}"</p>
                                            )}
                                        </div>
                                        <div className="flex gap-2">
                                            <Button variant="outline" size="sm">
                                                Edit
                                            </Button>
                                            <Button variant="outline" size="sm">
                                                Reschedule
                                            </Button>
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    )
}
