"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { ArrowLeft, Calendar, Clock, User, Phone, MapPin, FileText } from "lucide-react"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"

// Mock data for leads and agents
const mockLeads = [
    { id: "1", name: "<PERSON>", phone: "(*************", email: "<EMAIL>" },
    { id: "2", name: "<PERSON>", phone: "(*************", email: "<EMAIL>" },
    { id: "3", name: "<PERSON>", phone: "(*************", email: "<EMAIL>" },
    { id: "4", name: "<PERSON>", phone: "(*************", email: "<EMAIL>" },
]

const mockAgents = [
    { id: "1", name: "Downtown Auto Sales", phone: "(*************" },
    { id: "2", name: "Premium Motors", phone: "(*************" },
    { id: "3", name: "City Car Center", phone: "(*************" },
    { id: "4", name: "Elite Auto Group", phone: "(*************" },
]

const serviceTypes = [
    "Vehicle Inspection",
    "Test Drive",
    "Financing Discussion",
    "Trade-in Evaluation",
    "Service Consultation",
    "Parts Consultation",
    "Insurance Discussion",
    "Warranty Review",
]

export default function CreateAppointmentPage() {
    const router = useRouter()
    const [formData, setFormData] = useState({
        leadId: "",
        agentId: "",
        service: "",
        date: "",
        time: "",
        location: "",
        notes: "",
    })

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // Here you would typically send the data to your API
        console.log("Creating appointment:", formData)
        // Redirect back to appointments page
        router.push("/appointments")
    }

    const selectedLead = mockLeads.find((lead) => lead.id === formData.leadId)
    const selectedAgent = mockAgents.find((agent) => agent.id === formData.agentId)

    return (
        <div className="flex-1 space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="ghost" size="sm" onClick={() => router.back()}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Create Appointment</h1>
                    <p className="text-muted-foreground">Schedule a new appointment with a lead</p>
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Form */}
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointment Details</CardTitle>
                            <CardDescription>Fill in the information below to create a new appointment</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Lead Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="leadId">Select Lead *</Label>
                                    <Select
                                        value={formData.leadId}
                                        onValueChange={(value) => setFormData({ ...formData, leadId: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Choose a lead for this appointment" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {mockLeads.map((lead) => (
                                                <SelectItem key={lead.id} value={lead.id}>
                                                    <div className="flex items-center gap-2">
                                                        <User className="h-4 w-4" />
                                                        <span>{lead.name}</span>
                                                        <span className="text-muted-foreground">({lead.phone})</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Agent Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="agentId">Select Agent *</Label>
                                    <Select
                                        value={formData.agentId}
                                        onValueChange={(value) => setFormData({ ...formData, agentId: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Choose an agent for this appointment" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {mockAgents.map((agent) => (
                                                <SelectItem key={agent.id} value={agent.id}>
                                                    <div className="flex items-center gap-2">
                                                        <Phone className="h-4 w-4" />
                                                        <span>{agent.name}</span>
                                                        <span className="text-muted-foreground">({agent.phone})</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <Separator />

                                {/* Service Type */}
                                <div className="space-y-2">
                                    <Label htmlFor="service">Service Type *</Label>
                                    <Select
                                        value={formData.service}
                                        onValueChange={(value) => setFormData({ ...formData, service: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select the type of service" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {serviceTypes.map((service) => (
                                                <SelectItem key={service} value={service}>
                                                    {service}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Date and Time */}
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="date">Date *</Label>
                                        <div className="relative">
                                            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="date"
                                                type="date"
                                                value={formData.date}
                                                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                                                className="pl-10"
                                                required
                                            />
                                        </div>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="time">Time *</Label>
                                        <div className="relative">
                                            <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="time"
                                                type="time"
                                                value={formData.time}
                                                onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                                                className="pl-10"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Location */}
                                <div className="space-y-2">
                                    <Label htmlFor="location">Location</Label>
                                    <div className="relative">
                                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="location"
                                            placeholder="Enter appointment location"
                                            value={formData.location}
                                            onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>

                                {/* Notes */}
                                <div className="space-y-2">
                                    <Label htmlFor="notes">Notes</Label>
                                    <div className="relative">
                                        <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Textarea
                                            id="notes"
                                            placeholder="Add any additional notes or special instructions"
                                            value={formData.notes}
                                            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                            className="pl-10 min-h-[100px]"
                                        />
                                    </div>
                                </div>

                                {/* Submit Buttons */}
                                <div className="flex gap-4 pt-4">
                                    <Button type="submit" className="flex-1">
                                        Create Appointment
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => router.back()}>
                                        Cancel
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Summary Sidebar */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointment Summary</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {selectedLead ? (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Lead Information</h4>
                                    <div className="text-sm space-y-1">
                                        <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedLead.name}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedLead.phone}</span>
                                        </div>
                                        <div className="text-muted-foreground">{selectedLead.email}</div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-sm text-muted-foreground">Select a lead to see their information</div>
                            )}

                            <Separator />

                            {selectedAgent ? (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Agent Information</h4>
                                    <div className="text-sm space-y-1">
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedAgent.name}</span>
                                        </div>
                                        <div className="text-muted-foreground">{selectedAgent.phone}</div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-sm text-muted-foreground">Select an agent to see their information</div>
                            )}

                            <Separator />

                            {(formData.date || formData.time || formData.service) && (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Appointment Details</h4>
                                    <div className="text-sm space-y-1">
                                        {formData.service && (
                                            <div>
                                                Service: <span className="font-medium">{formData.service}</span>
                                            </div>
                                        )}
                                        {formData.date && (
                                            <div>
                                                Date: <span className="font-medium">{formData.date}</span>
                                            </div>
                                        )}
                                        {formData.time && (
                                            <div>
                                                Time: <span className="font-medium">{formData.time}</span>
                                            </div>
                                        )}
                                        {formData.location && (
                                            <div>
                                                Location: <span className="font-medium">{formData.location}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-sm">Quick Tips</CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-muted-foreground space-y-2">
                            <p>• Double-check the date and time before creating</p>
                            <p>• Include location details for better coordination</p>
                            <p>• Add notes about specific lead requirements</p>
                            <p>• Confirm agent availability before scheduling</p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
