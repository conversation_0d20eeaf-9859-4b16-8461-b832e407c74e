"use client"

import { useState, use } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import {
    ArrowLeft,
    Calendar,
    Clock,
    User,
    Phone,
    MapPin,
    MessageSquare,
    DollarSign,
    Edit3,
    Save,
    X,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
} from "lucide-react"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Separator } from "@app/components/ui/separator"
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"

// Mock appointment data
const mockAppointment = {
    id: "1",
    leadId: "lead-1",
    leadName: "<PERSON>",
    leadPhone: "(*************",
    leadEmail: "<EMAIL>",
    leadAddress: "123 Oak Street, Springfield, IL 62701",
    agentId: "agent-1",
    agentName: "Downtown Auto Sales",
    agentPhone: "(*************",
    service: "Vehicle Inspection",
    date: "2024-01-15",
    time: "10:00 AM",
    value: 25000,
    notes: "Interested in 2023 Honda Civic. First-time buyer, needs financing options.",
    status: "confirmed",
    location: "123 Main St, Downtown",
    createdAt: "2024-01-10T09:00:00Z",
    updatedAt: "2024-01-12T14:30:00Z",
}

// Mock conversation data
const mockConversations = [
    {
        id: "conv-1",
        type: "call",
        direction: "inbound",
        duration: "4:32",
        timestamp: "2024-01-12T14:30:00Z",
        summary: "Lead called asking about vehicle availability and pricing",
    },
    {
        id: "conv-2",
        type: "sms",
        messageCount: 8,
        timestamp: "2024-01-11T16:45:00Z",
        lastMessage: "Great! I'll see you on Monday at 10 AM for the inspection.",
    },
    {
        id: "conv-3",
        type: "call",
        direction: "outbound",
        duration: "2:15",
        timestamp: "2024-01-10T11:20:00Z",
        summary: "Follow-up call to schedule appointment",
    },
]

// Mock AI summary
const aiSummary = {
    jobType: "Vehicle Purchase Consultation",
    leadIntent: "High - actively looking to purchase within 2 weeks",
    keyRequirements: [
        "Reliable sedan under $30,000",
        "Good fuel economy for daily commuting",
        "Financing options with low down payment",
        "Trade-in evaluation for current vehicle",
    ],
    recommendedApproach:
        "Focus on certified pre-owned vehicles with warranty coverage. Emphasize financing flexibility and total cost of ownership.",
    riskFactors: ["First-time buyer may need education on financing", "Price-sensitive - may shop around"],
    opportunityScore: 85,
}

interface AppointmentDetailsPageProps {
    params: Promise<{
        id: string
    }>;
}

export default function AppointmentDetailsPage(props: AppointmentDetailsPageProps) {
    const params = use(props.params);
    const router = useRouter()
    const [isEditing, setIsEditing] = useState(false)
    const [editData, setEditData] = useState({
        date: mockAppointment.date,
        time: mockAppointment.time,
        value: mockAppointment.value.toString(),
        notes: mockAppointment.notes,
    })

    const handleSave = () => {
        // Here you would typically send the data to your API
        console.log("Saving appointment:", editData)
        setIsEditing(false)
    }

    const handleCancel = () => {
        setEditData({
            date: mockAppointment.date,
            time: mockAppointment.time,
            value: mockAppointment.value.toString(),
            notes: mockAppointment.notes,
        })
        setIsEditing(false)
    }

    return (
        <div className="flex-1 space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={() => router.back()}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Schedule
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Appointment Details</h1>
                        <p className="text-muted-foreground">
                            {mockAppointment.service} with {mockAppointment.leadName}
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    {isEditing ? (
                        <>
                            <Button onClick={handleSave} size="sm">
                                <Save className="mr-2 h-4 w-4" />
                                Save Changes
                            </Button>
                            <Button variant="outline" onClick={handleCancel} size="sm">
                                <X className="mr-2 h-4 w-4" />
                                Cancel
                            </Button>
                        </>
                    ) : (
                        <Button onClick={() => setIsEditing(true)} size="sm">
                            <Edit3 className="mr-2 h-4 w-4" />
                            Edit Appointment
                        </Button>
                    )}
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Appointment Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Appointment Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label>Date</Label>
                                    {isEditing ? (
                                        <Input
                                            type="date"
                                            value={editData.date}
                                            onChange={(e) => setEditData({ ...editData, date: e.target.value })}
                                        />
                                    ) : (
                                        <div className="flex items-center gap-2 text-sm">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            {new Date(mockAppointment.date).toLocaleDateString()}
                                        </div>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label>Time</Label>
                                    {isEditing ? (
                                        <Input
                                            type="time"
                                            value={editData.time}
                                            onChange={(e) => setEditData({ ...editData, time: e.target.value })}
                                        />
                                    ) : (
                                        <div className="flex items-center gap-2 text-sm">
                                            <Clock className="h-4 w-4 text-muted-foreground" />
                                            {mockAppointment.time}
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label>Estimated Value</Label>
                                {isEditing ? (
                                    <Input
                                        type="number"
                                        value={editData.value}
                                        onChange={(e) => setEditData({ ...editData, value: e.target.value })}
                                        placeholder="Enter estimated value"
                                    />
                                ) : (
                                    <div className="flex items-center gap-2 text-sm">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />${mockAppointment.value.toLocaleString()}
                                    </div>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label>Notes</Label>
                                {isEditing ? (
                                    <Textarea
                                        value={editData.notes}
                                        onChange={(e) => setEditData({ ...editData, notes: e.target.value })}
                                        placeholder="Add appointment notes"
                                        className="min-h-[100px]"
                                    />
                                ) : (
                                    <div className="text-sm bg-muted/50 p-3 rounded-lg">{mockAppointment.notes}</div>
                                )}
                            </div>

                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <div>
                                    <MapPin className="inline h-4 w-4 mr-1" />
                                    {mockAppointment.location}
                                </div>
                                <Badge variant="secondary">{mockAppointment.status}</Badge>
                            </div>
                        </CardContent>
                    </Card>

                    {/* AI Job Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Sparkles className="h-5 w-5" />
                                AI Job Summary
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-medium mb-2">Job Type</h4>
                                    <p className="text-sm text-muted-foreground">{aiSummary.jobType}</p>
                                </div>
                                <div>
                                    <h4 className="font-medium mb-2">Lead Intent</h4>
                                    <p className="text-sm text-muted-foreground">{aiSummary.leadIntent}</p>
                                </div>
                            </div>

                            <div>
                                <h4 className="font-medium mb-2">Key Requirements</h4>
                                <ul className="text-sm text-muted-foreground space-y-1">
                                    {aiSummary.keyRequirements.map((req, index) => (
                                        <li key={index} className="flex items-start gap-2">
                                            <span className="text-primary-300">•</span>
                                            {req}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div>
                                <h4 className="font-medium mb-2">Recommended Approach</h4>
                                <p className="text-sm text-muted-foreground">{aiSummary.recommendedApproach}</p>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-medium mb-2">Risk Factors</h4>
                                    <ul className="text-sm text-muted-foreground space-y-1">
                                        {aiSummary.riskFactors.map((risk, index) => (
                                            <li key={index} className="flex items-start gap-2">
                                                <span className="text-destructive">•</span>
                                                {risk}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                <div>
                                    <h4 className="font-medium mb-2">Opportunity Score</h4>
                                    <div className="flex items-center gap-2">
                                        <div className="flex-1 bg-muted rounded-full h-2">
                                            <div
                                                className="bg-primary-300 h-2 rounded-full"
                                                style={{ width: `${aiSummary.opportunityScore}%` }}
                                            />
                                        </div>
                                        <span className="text-sm font-medium">{aiSummary.opportunityScore}%</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Conversations */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MessageSquare className="h-5 w-5" />
                                Recent Conversations
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {mockConversations.map((conversation) => (
                                    <div key={conversation.id} className="flex items-start gap-3 p-3 rounded-lg border">
                                        <div className="flex-shrink-0">
                                            {conversation.type === "call" ? (
                                                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                                    <Phone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                            ) : (
                                                <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                                    <MessageSquare className="h-4 w-4 text-green-600 dark:text-green-400" />
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="text-sm font-medium capitalize">{conversation.type}</span>
                                                {conversation.type === "call" && (
                                                    <>
                                                        <Badge variant="outline" className="text-xs">
                                                            {conversation.direction}
                                                        </Badge>
                                                        <span className="text-xs text-muted-foreground">{conversation.duration}</span>
                                                    </>
                                                )}
                                                {conversation.type === "sms" && (
                                                    <Badge variant="outline" className="text-xs">
                                                        {conversation.messageCount} messages
                                                    </Badge>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {conversation.summary || conversation.lastMessage}
                                            </p>
                                            <p className="text-xs text-muted-foreground mt-1">
                                                {new Date(conversation.timestamp).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Lead Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Lead Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Avatar>
                                    <AvatarFallback>
                                        <AvatarInitials name={mockAppointment.leadName} />
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <h3 className="font-semibold">{mockAppointment.leadName}</h3>
                                    <p className="text-sm text-muted-foreground">Lead ID: {mockAppointment.leadId}</p>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex items-center gap-2 text-sm">
                                    <Phone className="h-4 w-4 text-muted-foreground" />
                                    <Link href={`tel:${mockAppointment.leadPhone}`} className="hover:underline">
                                        {mockAppointment.leadPhone}
                                    </Link>
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                                    <Link href={`mailto:${mockAppointment.leadEmail}`} className="hover:underline">
                                        {mockAppointment.leadEmail}
                                    </Link>
                                </div>
                                <div className="flex items-start gap-2 text-sm">
                                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <span>{mockAppointment.leadAddress}</span>
                                </div>
                            </div>

                            <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                                <Link href={`/leads/${mockAppointment.leadId}`}>View Full Lead Profile</Link>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Agent Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Bot className="h-5 w-5" />
                                Agent Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Avatar>
                                    <AvatarFallback>
                                        <AvatarInitials name={mockAppointment.agentName} />
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <h3 className="font-semibold">{mockAppointment.agentName}</h3>
                                    <p className="text-sm text-muted-foreground">Agent ID: {mockAppointment.agentId}</p>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                <div className="flex items-center gap-2 text-sm">
                                    <Phone className="h-4 w-4 text-muted-foreground" />
                                    <Link href={`tel:${mockAppointment.agentPhone}`} className="hover:underline">
                                        {mockAppointment.agentPhone}
                                    </Link>
                                </div>
                                <div className="text-sm">
                                    <span className="text-muted-foreground">Service:</span>
                                    <span className="ml-2 font-medium">{mockAppointment.service}</span>
                                </div>
                            </div>

                            <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                                <Link href={`/agents/${mockAppointment.agentId}`}>View Agent Details</Link>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <Phone className="mr-2 h-4 w-4" />
                                Call Lead
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Send Message
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <Calendar className="mr-2 h-4 w-4" />
                                Reschedule
                            </Button>
                            <Button variant="destructive" size="sm" className="w-full justify-start">
                                <X className="mr-2 h-4 w-4" />
                                Cancel Appointment
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
