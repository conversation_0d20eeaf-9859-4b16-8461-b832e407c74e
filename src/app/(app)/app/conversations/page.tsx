"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"
import { Input } from "@app/components/ui/input"
import {
    MessageSquare,
    Search,
    Phone,
    Clock,
    Bot,
    User,
    MoreHorizontal,
    Reply,
    Send,
    ArrowLeft,
    Calendar,
    PhoneCall,
    Voicemail,
    Play,
} from "lucide-react"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@app/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Switch } from "@app/components/ui/switch"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { cn } from "@app/lib/utils"

export default function ConversationsPage() {
    const [selectedConversation, setSelectedConversation] = useState<string | null>(null)
    const [newMessage, setNewMessage] = useState("")
    const [autoResponseEnabled, setAutoResponseEnabled] = useState(true)

    // Mock data for conversations
    const conversations = [
        {
            id: "1",
            leadName: "Sarah Johnson",
            leadPhone: "(*************",
            agentName: "Main Business Line",
            status: "active",
            lastMessage: "Thanks for the quick response! When can we schedule?",
            lastMessageTime: "2 minutes ago",
            messageCount: 8,
            isUnread: true,
            priority: "high",
            createdAt: "2024-03-15T10:30:00Z",
            service: "Plumbing repair",
        },
        {
            id: "2",
            leadName: "Mike Chen",
            leadPhone: "(*************",
            agentName: "Emergency Line",
            status: "resolved",
            lastMessage: "Perfect, see you tomorrow at 2 PM!",
            lastMessageTime: "1 hour ago",
            messageCount: 12,
            isUnread: false,
            priority: "medium",
            createdAt: "2024-03-15T09:15:00Z",
            service: "HVAC maintenance",
        },
        {
            id: "3",
            leadName: "Emily Davis",
            leadPhone: "(*************",
            agentName: "Sales Inquiries",
            status: "pending",
            lastMessage: "I'd like to get a quote for the kitchen renovation",
            lastMessageTime: "3 hours ago",
            messageCount: 5,
            isUnread: true,
            priority: "high",
            createdAt: "2024-03-15T08:45:00Z",
            service: "Kitchen renovation",
        },
        {
            id: "4",
            leadName: "Robert Brown",
            leadPhone: "(*************",
            agentName: "Customer Support",
            status: "active",
            lastMessage: "The issue has been resolved, thank you!",
            lastMessageTime: "5 hours ago",
            messageCount: 15,
            isUnread: false,
            priority: "low",
            createdAt: "2024-03-14T16:20:00Z",
            service: "Technical support",
        },
        {
            id: "5",
            leadName: "Lisa Wilson",
            leadPhone: "(*************",
            agentName: "Main Business Line",
            status: "archived",
            lastMessage: "Thanks for the excellent service!",
            lastMessageTime: "2 days ago",
            messageCount: 22,
            isUnread: false,
            priority: "medium",
            createdAt: "2024-03-13T14:10:00Z",
            service: "Bathroom repair",
        },
    ]

    // Mock data for conversation messages and calls
    const mockMessages = [
        {
            id: "1",
            type: "message",
            content: "Hi, I'm calling about a plumbing issue in my kitchen. The sink is backing up.",
            sender: "lead",
            senderName: "Sarah Johnson",
            timestamp: "2024-03-15T10:30:00Z",
        },
        {
            id: "2",
            type: "call",
            duration: "2:34",
            hasVoicemail: true,
            voicemailTranscription:
                "Hi, this is Sarah Johnson calling about the kitchen sink issue. Please call me back at your earliest convenience.",
            timestamp: "2024-03-15T10:32:00Z",
        },
        {
            id: "3",
            type: "message",
            content: "Thanks for reaching out! I can help you with that. What type of backup are you experiencing?",
            sender: "agent",
            senderName: "Main Business Line",
            timestamp: "2024-03-15T10:35:00Z",
        },
        {
            id: "4",
            type: "message",
            content: "The water won't drain at all, and there's a bad smell coming from the disposal.",
            sender: "lead",
            senderName: "Sarah Johnson",
            timestamp: "2024-03-15T10:37:00Z",
        },
        {
            id: "5",
            type: "message",
            content:
                "That sounds like a clog in your disposal or drain line. I can schedule a technician to come take a look. Are you available tomorrow morning?",
            sender: "agent",
            senderName: "Main Business Line",
            timestamp: "2024-03-15T10:40:00Z",
        },
        {
            id: "6",
            type: "message",
            content: "Thanks for the quick response! When can we schedule?",
            sender: "lead",
            senderName: "Sarah Johnson",
            timestamp: "2024-03-15T10:42:00Z",
        },
    ]

    const getStatusColor = (status: string) => {
        switch (status) {
            case "active":
                return "default"
            case "pending":
                return "destructive"
            case "resolved":
                return "secondary"
            case "archived":
                return "outline"
            default:
                return "secondary"
        }
    }

    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case "high":
                return "text-red-500"
            case "medium":
                return "text-yellow-500"
            case "low":
                return "text-green-500"
            default:
                return "text-gray-500"
        }
    }

    const stats = [
        {
            title: "Total Conversations",
            value: conversations.length.toString(),
            description: "All time",
            icon: MessageSquare,
        },
        {
            title: "Active Conversations",
            value: conversations.filter((c) => c.status === "active").length.toString(),
            description: "Ongoing discussions",
            icon: User,
        },
        {
            title: "Pending Response",
            value: conversations.filter((c) => c.status === "pending").length.toString(),
            description: "Awaiting reply",
            icon: Clock,
        },
        {
            title: "Avg Response Time",
            value: "12m",
            description: "This week",
            icon: Reply,
        },
    ]

    const selectedConversationData = conversations.find((c) => c.id === selectedConversation)

    const handleConversationClick = (conversationId: string) => {
        setSelectedConversation(conversationId)
    }

    const handleSendMessage = () => {
        if (newMessage.trim()) {
            // Here you would send the message via API
            console.log("Sending message:", newMessage)
            setNewMessage("")
        }
    }

    const renderMessage = (message: any) => {
        if (message.type === "call") {
            return (
                <div key={message.id} className="flex justify-center my-4">
                    <div className="bg-muted rounded-lg p-3 max-w-md text-center">
                        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-2">
                            <PhoneCall className="h-4 w-4" />
                            <span>Incoming call • {message.duration}</span>
                        </div>
                        {message.hasVoicemail && (
                            <div className="mt-2 p-2 bg-background rounded border">
                                <div className="flex items-center gap-2 mb-2">
                                    <Voicemail className="h-4 w-4" />
                                    <span className="text-sm font-medium">Voicemail</span>
                                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                        <Play className="h-3 w-3" />
                                    </Button>
                                </div>
                                <p className="text-xs text-muted-foreground italic">"{message.voicemailTranscription}"</p>
                            </div>
                        )}
                    </div>
                </div>
            )
        }

        const isLead = message.sender === "lead"
        return (
            <div key={message.id} className={cn("flex mb-4", isLead ? "justify-end" : "justify-start")}>
                <div className={cn("max-w-[70%] rounded-lg p-3", isLead ? "bg-primary-800 text-primary" : "bg-muted")}>
                    <div className="text-xs opacity-70 mb-1">{message.senderName}</div>
                    <div className="text-sm">{message.content}</div>
                    <div className="text-xs opacity-70 mt-1">{new Date(message.timestamp).toLocaleTimeString()}</div>
                </div>
            </div>
        )
    }

    return (
        <div className="flex h-[calc(100vh-8rem)] gap-6">
            {/* Left Column - Conversations List */}
            <div
                className={cn(
                    "space-y-6 transition-all duration-300",
                    selectedConversation ? "w-1/4 min-w-[300px]" : "w-full",
                    "md:block",
                    selectedConversation ? "hidden md:block" : "block",
                )}
            >
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Conversations</h1>
                        <p className="text-sm text-muted-foreground">Manage agent conversations</p>
                    </div>
                </div>

                {/* Stats Overview - Condensed */}
                <div className="grid gap-2 grid-cols-2">
                    {stats.slice(0, 4).map((stat) => (
                        <Card key={stat.title} className="p-3">
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="text-lg font-bold">{stat.value}</div>
                                    <p className="text-xs text-muted-foreground">{stat.title}</p>
                                </div>
                                <stat.icon className="h-4 w-4 text-muted-foreground" />
                            </div>
                        </Card>
                    ))}
                </div>

                {/* Search and Filters - Condensed */}
                <div className="space-y-2">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input placeholder="Search..." className="pl-9" />
                    </div>
                    <div className="flex gap-2">
                        <Select defaultValue="all">
                            <SelectTrigger className="flex-1">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="resolved">Resolved</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Conversations List - Condensed */}
                <Card className="flex-1">
                    <CardContent className="p-0">
                        <div className="divide-y max-h-[400px] overflow-y-auto">
                            {conversations.map((conversation) => (
                                <div
                                    key={conversation.id}
                                    className={cn(
                                        "p-3 hover:bg-muted/50 transition-colors cursor-pointer",
                                        conversation.isUnread && "bg-muted/30",
                                        selectedConversation === conversation.id && "bg-muted border-l-4 border-l-primary-300",
                                    )}
                                    onClick={() => handleConversationClick(conversation.id)}
                                >
                                    <div className="flex items-start space-x-3">
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback>
                                                <AvatarInitials name={conversation.leadName} />
                                            </AvatarFallback>
                                        </Avatar>

                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-2">
                                                <p className={cn("font-medium text-sm truncate", conversation.isUnread && "font-semibold")}>
                                                    {conversation.leadName}
                                                </p>
                                                {conversation.isUnread && <div className="h-2 w-2 bg-primary-500 rounded-full flex-shrink-0" />}
                                            </div>

                                            <p className="text-xs text-muted-foreground truncate">{conversation.lastMessage}</p>

                                            <div className="flex items-center justify-between mt-1">
                                                <Badge variant={getStatusColor(conversation.status)} className="text-xs">
                                                    {conversation.status}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">{conversation.lastMessageTime}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Right Column - Conversation Messages */}
            {selectedConversation && selectedConversationData && (
                <div
                    className={cn(
                        "flex-1 flex flex-col transition-all duration-300",
                        "fixed inset-0 z-50 bg-background md:relative md:z-auto",
                        "md:block",
                    )}
                >
                    <div className="md:hidden flex items-center gap-2 p-4 border-b">
                        <Button variant="ghost" size="sm" onClick={() => setSelectedConversation(null)}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            All Conversations
                        </Button>
                    </div>

                    {/* Conversation Header */}
                    <Card className="mb-4">
                        <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    <Avatar className="h-10 w-10">
                                        <AvatarFallback>
                                            <AvatarInitials name={selectedConversationData.leadName} />
                                        </AvatarFallback>
                                    </Avatar>
                                    <div>
                                        <CardTitle className="text-lg">{selectedConversationData.leadName}</CardTitle>
                                        <CardDescription className="flex items-center gap-2">
                                            <Phone className="h-3 w-3" />
                                            {selectedConversationData.leadPhone}
                                            <span>•</span>
                                            <Bot className="h-3 w-3" />
                                            {selectedConversationData.agentName}
                                        </CardDescription>
                                    </div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button variant="outline" size="sm">
                                        <Calendar className="h-4 w-4 mr-2" />
                                        Schedule
                                    </Button>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem>View Lead Details</DropdownMenuItem>
                                            <DropdownMenuItem>Mark Important</DropdownMenuItem>
                                            <DropdownMenuItem>Archive</DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>

                            {/* Conversation Controls */}
                            <div className="flex items-center justify-between pt-4 border-t">
                                <div className="flex items-center gap-4">
                                    <Select defaultValue={selectedConversationData.priority}>
                                        <SelectTrigger className="w-32">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="high">High Priority</SelectItem>
                                            <SelectItem value="medium">Medium Priority</SelectItem>
                                            <SelectItem value="low">Low Priority</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Select defaultValue={selectedConversationData.status}>
                                        <SelectTrigger className="w-32">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="active">Active</SelectItem>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="resolved">Resolved</SelectItem>
                                            <SelectItem value="archived">Archived</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Switch id="auto-response" checked={autoResponseEnabled} onCheckedChange={setAutoResponseEnabled} />
                                    <Label htmlFor="auto-response" className="text-sm">
                                        Auto-response
                                    </Label>
                                </div>
                            </div>

                            {/* Metrics */}
                            <div className="grid grid-cols-4 gap-4 pt-4 border-t">
                                <div className="text-center">
                                    <div className="text-lg font-semibold">{selectedConversationData.messageCount}</div>
                                    <div className="text-xs text-muted-foreground">Messages</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-lg font-semibold">12m</div>
                                    <div className="text-xs text-muted-foreground">Avg Response</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-lg font-semibold">85%</div>
                                    <div className="text-xs text-muted-foreground">Sentiment</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-lg font-semibold">$450</div>
                                    <div className="text-xs text-muted-foreground">Est. Value</div>
                                </div>
                            </div>
                        </CardHeader>
                    </Card>

                    {/* Messages Area */}
                    <Card className="flex-1 flex flex-col">
                        <CardContent className="flex-1 p-4 overflow-y-auto max-h-[400px]">
                            {mockMessages.map(renderMessage)}
                        </CardContent>

                        {/* Message Input */}
                        <div className="border-t p-4">
                            <div className="flex gap-2">
                                <Textarea
                                    placeholder="Type your message..."
                                    value={newMessage}
                                    onChange={(e) => setNewMessage(e.target.value)}
                                    className="flex-1 min-h-[60px] resize-none"
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" && !e.shiftKey) {
                                            e.preventDefault()
                                            handleSendMessage()
                                        }
                                    }}
                                />
                                <Button onClick={handleSendMessage} disabled={!newMessage.trim()} className="self-end">
                                    <Send className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </Card>
                </div>
            )}
        </div>
    )
}
