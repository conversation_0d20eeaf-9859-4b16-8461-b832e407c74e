"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { But<PERSON> } from "@app/components/ui/button"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { ChevronLeft, ChevronRight, User } from "lucide-react"
import { Skeleton } from "@app/components/ui/skeleton"

interface Author {
    id: number
    name: string
}

interface Post {
    id: number
    author: Author
    title: string
    content: string
}

interface PostsResponse {
    status: number
    data: Post[]
    pagination: {
        limit: number
        page: number
    }
}

export default function DevPage() {
    const [posts, setPosts] = useState<Post[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const limit = 10

    const fetchPosts = async (page: number) => {
        setLoading(true)
        setError(null)

        try {
            const response = await fetch(`https://api.back-talk.ai/api/post?paginate[page]=${page}&paginate[limit]=${limit}`)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data: PostsResponse = await response.json()

            setPosts(data.data)
            setCurrentPage(data.pagination.page)
            // Calculate total pages (you might need to adjust this based on your API response)
            setTotalPages(Math.ceil(data.data.length > 0 ? 10 : 1)) // Placeholder calculation

        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch posts')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchPosts(currentPage)
    }, [currentPage])

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page)
        }
    }

    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }

    if (error) {
        return (
            <div className="p-6">
                <Card>
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Error: {error}</p>
                        <Button onClick={() => fetchPosts(currentPage)} className="mt-4">
                            Retry
                        </Button>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="p-6 space-y-6">
            <div>
                <h1 className="text-3xl font-bold">Posts</h1>
                <p className="text-muted-foreground">Browse all posts from the community</p>
            </div>

            {loading ? (
                <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                        <Card key={i}>
                            <CardHeader>
                                <div className="flex items-center space-x-3">
                                    <Skeleton className="h-10 w-10 rounded-full" />
                                    <div className="space-y-2">
                                        <Skeleton className="h-4 w-32" />
                                        <Skeleton className="h-3 w-24" />
                                    </div>
                                </div>
                                <Skeleton className="h-6 w-3/4" />
                            </CardHeader>
                            <CardContent>
                                <Skeleton className="h-20 w-full" />
                            </CardContent>
                        </Card>
                    ))}
                </div>
            ) : (
                <>
                    <div className="space-y-4">
                        {posts.map((post) => (
                            <Card key={post.id}>
                                <CardHeader>
                                    <div className="flex items-center space-x-3 mb-3">
                                        <Avatar className="h-10 w-10">
                                            <AvatarFallback>
                                                {getInitials(post.author.name)}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <p className="font-medium">{post.author.name}</p>
                                            <p className="text-sm text-muted-foreground">Author ID: {post.author.id}</p>
                                        </div>
                                    </div>
                                    <CardTitle className="text-xl">{post.title}</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-muted-foreground leading-relaxed">{post.content}</p>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* Pagination */}
                    <div className="flex items-center justify-between">
                        <p className="text-sm text-muted-foreground">
                            Page {currentPage} of {totalPages}
                        </p>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage <= 1}
                            >
                                <ChevronLeft className="h-4 w-4" />
                                Previous
                            </Button>

                            {/* Page numbers */}
                            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                const pageNum = Math.max(1, currentPage - 2) + i
                                if (pageNum > totalPages) return null

                                return (
                                    <Button
                                        key={pageNum}
                                        variant={pageNum === currentPage ? "default" : "outline"}
                                        size="sm"
                                        onClick={() => handlePageChange(pageNum)}
                                    >
                                        {pageNum}
                                    </Button>
                                )
                            })}

                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage >= totalPages}
                            >
                                Next
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </>
            )}
        </div>
    )
}
