import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"
import { Phone, MessageSquare, Users, TrendingUp, Clock, PhoneCall, Calendar, ArrowUpRight } from "lucide-react"

export default function DashboardPage() {
    const breadcrumbs = [{ label: "Dashboard" }]

    // Mock data for dashboard metrics
    const stats = [
        {
            title: "Missed Calls Today",
            value: "12",
            change: "+3 from yesterday",
            icon: Phone,
            trend: "up",
        },
        {
            title: "Auto-Responses Sent",
            value: "28",
            change: "+8 from yesterday",
            icon: MessageSquare,
            trend: "up",
        },
        {
            title: "New Leads",
            value: "7",
            change: "+2 from yesterday",
            icon: Users,
            trend: "up",
        },
        {
            title: "Response Rate",
            value: "94%",
            change: "+2% from last week",
            icon: TrendingUp,
            trend: "up",
        },
    ]

    const recentLeads = [
        {
            name: "<PERSON>",
            phone: "(*************",
            time: "2 minutes ago",
            status: "new",
            service: "Plumbing repair",
        },
        {
            name: "Mike Chen",
            phone: "(*************",
            time: "15 minutes ago",
            status: "responded",
            service: "HVAC maintenance",
        },
        {
            name: "Emily Davis",
            phone: "(*************",
            time: "1 hour ago",
            status: "scheduled",
            service: "Electrical work",
        },
    ]

    const upcomingAppointments = [
        {
            client: "John Smith",
            service: "Kitchen renovation consultation",
            time: "Today, 2:00 PM",
            phone: "(*************",
        },
        {
            client: "Lisa Wilson",
            service: "Bathroom repair estimate",
            time: "Tomorrow, 10:30 AM",
            phone: "(*************",
        },
        {
            client: "Robert Brown",
            service: "Roof inspection",
            time: "Friday, 9:00 AM",
            phone: "(*************",
        },
    ]

    return (
            <div className="space-y-6">
                {/* Stats Overview */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {stats.map((stat) => (
                        <Card key={stat.title}>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">{stat.title}</CardTitle>
                                <stat.icon className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stat.value}</div>
                                <p className="text-xs text-muted-foreground flex items-center gap-1">
                                    <ArrowUpRight className="h-3 w-3 text-green-500" />
                                    {stat.change}
                                </p>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                    {/* Recent Leads */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Recent Leads
                            </CardTitle>
                            <CardDescription>New leads from missed calls and auto-responses</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {recentLeads.map((lead, index) => (
                                <div key={index} className="flex items-center justify-between space-x-4">
                                    <div className="flex items-center space-x-3">
                                        <Avatar className="h-8 w-8">
                                            <AvatarFallback>
                                                <AvatarInitials name={lead.name} />
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium leading-none">{lead.name}</p>
                                            <p className="text-xs text-muted-foreground">{lead.service}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Badge
                                            variant={
                                                lead.status === "new" ? "default" : lead.status === "responded" ? "secondary" : "outline"
                                            }
                                            className="text-xs"
                                        >
                                            {lead.status}
                                        </Badge>
                                        <p className="text-xs text-muted-foreground">{lead.time}</p>
                                    </div>
                                </div>
                            ))}
                            <Button variant="outline" className="w-full mt-4 bg-transparent">
                                View All Leads
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Upcoming Appointments */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Upcoming Appointments
                            </CardTitle>
                            <CardDescription>Scheduled appointments from converted leads</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {upcomingAppointments.map((appointment, index) => (
                                <div key={index} className="flex items-start justify-between space-x-4">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium leading-none">{appointment.client}</p>
                                        <p className="text-xs text-muted-foreground">{appointment.service}</p>
                                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <Clock className="h-3 w-3" />
                                            {appointment.time}
                                        </div>
                                    </div>
                                    <Button variant="ghost" size="sm">
                                        <PhoneCall className="h-4 w-4" />
                                    </Button>
                                </div>
                            ))}
                            <Button variant="outline" className="w-full mt-4 bg-transparent">
                                View Calendar
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common tasks and shortcuts for managing your Back-Talk service</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-3 md:grid-cols-3">
                            <Button className="justify-start bg-transparent" variant="outline">
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Create New Agent
                            </Button>
                            <Button className="justify-start bg-transparent" variant="outline">
                                <Phone className="mr-2 h-4 w-4" />
                                Test Auto-Response
                            </Button>
                            <Button className="justify-start bg-transparent" variant="outline">
                                <Users className="mr-2 h-4 w-4" />
                                Export Leads
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
    )
}
