"use client";

import {useState, useEffect} from "react";
import {<PERSON>, CardHeader, CardTitle} from "@app/components/ui/card";
import {Button} from "@app/components/ui/button";
import {Avatar, AvatarFallback} from "@app/components/ui/avatar";
import {PhoneNumber} from "@app/components/ui/phoneNumber";
import {Badge} from "@app/components/ui/badge";
import Link from "next/link"
import Agent from "@app/types/agent";

interface LeadListProps {
    agent?: string,
    title?: string,
    description?: string,
    limit?: number,
    sort?: string,
}

export default function LeadList({agent, title, description, limit, sort}: LeadListProps)
{
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [leads, setLeads] = useState<Lead[]>([]);

    const fetchLeads = async () => {
        setLoading(true)
        setError(null)
        try {

            const response = await fetch(`https://api.back-talk.ai/api/v1/lead?paginate[limit]=${limit || 5}&sort=${sort || 'createdAt'}`)

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            setLeads(data.data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch leads')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchLeads();
    }, [agent])

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="text-lg">{title || "Leads"}</CardTitle>
                        <CardDescription>{description || "Leads generated from your agents"}</CardDescription>
                    </div>
                    {agent &&
                        <Button asChild  className="bg-primary-200">
                            <Link href={`/agents/${agent}/leads`} className="bg-primary-300">
                                View All
                            </Link>
                        </Button>
                    }
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {loading ? (
                        <p>Loading...</p>
                    ) : error ? (
                        <p className="text-red-500">Error: {error}</p>
                    ) : (
                        leads.length === 0 ? (
                            <p className="text-muted-foreground">No leads</p>
                        ) : (
                            leads.map((lead) => (
                                <Link href={`/leads/${lead.id}`} key={lead.id}>
                                    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                                        <div className="flex items-center space-x-3">
                                            <Avatar className="h-8 w-8">
                                                <AvatarFallback>
                                                    {lead.attributes.person.name.full
                                                        .split(" ")
                                                        .map((n) => n[0])
                                                        .join("")}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <p className="font-medium">{lead.attributes.person.name.full}</p>
                                                <p className="text-sm text-muted-foreground">{lead.attributes.person.phone}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <p className="text-sm font-medium">{lead.attributes.service}</p>
                                        </div>
                                    </div>
                                </Link>
                            ))
                        )
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
