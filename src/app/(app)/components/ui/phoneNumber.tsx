import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@app/lib/utils"

const phoneVariants = cva(
  "inline-flex items-center text-sm",
  {
    variants: {
      variant: {
        default: "text-foreground",
        muted: "text-muted-foreground",
        accent: "text-accent-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

/**
 * Formats a phone number to (************* format
 * Handles both 10-digit and 11-digit (with leading 1) numbers
 */
function formatPhoneNumber(phone: string): string {
    phone = phone.toString();
  const digits = phone.replace(/\D/g, "")
    // console.log(phone);
    // console.log(typeof phone);
  // const digits = phone;

  // Handle empty or invalid input
  if (!digits) return phone

  // Handle 11-digit number (with leading 1)
  if (digits.length === 11 && digits.startsWith("1")) {
    const areaCode = digits.slice(1, 4)
    const exchange = digits.slice(4, 7)
    const number = digits.slice(7, 11)
    return `(${areaCode}) ${exchange}-${number}`
  }

  // Handle 10-digit number
  if (digits.length === 10) {
    const areaCode = digits.slice(0, 3)
    const exchange = digits.slice(3, 6)
    const number = digits.slice(6, 10)
    return `(${areaCode}) ${exchange}-${number}`
  }

  // Return original if not a valid phone number length
  return phone
}

interface PhoneProps extends React.ComponentProps<"span">, VariantProps<typeof phoneVariants> {
  phone: string
  asChild?: boolean
}

function PhoneNumber({
  phone,
  className,
  variant,
  asChild = false,
  ...props
}: PhoneProps) {
  const Comp = asChild ? Slot : "span"
  const formattedPhone = formatPhoneNumber(phone)

  return (
    <Comp
      data-slot="phone"
      className={cn(phoneVariants({ variant }), className)}
      {...props}
    >
      {formattedPhone}
    </Comp>
  )
}

export { PhoneNumber, phoneVariants, formatPhoneNumber }
