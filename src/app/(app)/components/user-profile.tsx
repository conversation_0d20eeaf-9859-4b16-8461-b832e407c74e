"use client";

import { useAuth } from "@app/lib/use-auth";
import { apiClient } from "@app/lib/api-client";
import { Button } from "@app/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card";
import { Badge } from "@app/components/ui/badge";
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar";
import { LogOut, RefreshCw, User, Mail, Shield } from "lucide-react";
import { useState } from "react";

export function UserProfile() {
  const { user, isLoading, logout, refreshToken, error, hasOrganization, organizationId } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshToken = async () => {
    setIsRefreshing(true);
    try {
      await refreshToken();
    } catch (error) {
      console.error('Failed to refresh token:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  // Example of using the API client for a protected request
  const handleTestApiCall = async () => {
    try {
      // This will automatically include the JWT token
      const response = await apiClient.get('/api/v1/agent');
      console.log('API Response:', response);
    } catch (error) {
      console.error('API call failed:', error);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="animate-pulse rounded-full bg-gray-200 h-12 w-12"></div>
            <div className="space-y-2">
              <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
              <div className="animate-pulse bg-gray-200 h-3 w-24 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">No user data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Profile
        </CardTitle>
        <CardDescription>Your account information and authentication status</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Info */}
        <div className="flex items-center space-x-4">
          <Avatar className="h-12 w-12">
            <AvatarFallback>
              <AvatarInitials 
                name={`${user.person.firstName || ''} ${user.person.lastName || ''}`.trim() || user.username} 
              />
            </AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <h3 className="font-medium">
              {user.person.firstName && user.person.lastName 
                ? `${user.person.firstName} ${user.person.lastName}`
                : user.username
              }
            </h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Mail className="h-3 w-3" />
              {user.person.email || 'No email provided'}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={user.verified ? "default" : "secondary"} className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                {user.verified ? "Verified" : "Unverified"}
              </Badge>
              <Badge variant={hasOrganization ? "default" : "destructive"} className="text-xs">
                {hasOrganization ? "Organization Set" : "Needs Setup"}
              </Badge>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* User Details */}
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-muted-foreground">User ID:</span>
              <p className="font-mono text-xs mt-1 break-all">{user.id}</p>
            </div>
            <div>
              <span className="font-medium text-muted-foreground">Username:</span>
              <p className="mt-1">{user.username}</p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button 
            variant="outline" 
            onClick={handleRefreshToken}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh Token'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleTestApiCall}
            className="flex items-center gap-2"
          >
            Test API Call
          </Button>
          
          <Button 
            variant="destructive" 
            onClick={handleLogout}
            className="flex items-center gap-2"
          >
            <LogOut className="h-4 w-4" />
            Logout
          </Button>
        </div>

        {/* Organization Info */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-sm mb-2">Organization Status</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>Has Organization: {hasOrganization ? '✓ Yes' : '✗ No'}</p>
            <p>Organization ID: {organizationId || 'Not set'}</p>
            {!hasOrganization && (
              <p className="text-amber-600">⚠ Organization setup required</p>
            )}
          </div>
        </div>

        {/* API Client Info */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-sm mb-2">API Client Status</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>Token stored: {apiClient.isAuthenticated() ? '✓ Yes' : '✗ No'}</p>
            <p>Base URL: {process.env.NEXT_PUBLIC_API_URL || 'https://api.back-talk.ai'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
