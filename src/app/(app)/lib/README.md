# Back-Talk Authentication System

This directory contains the authentication system for the Back-Talk app, including JWT token management and API client functionality.

## Files Overview

### `api-client.ts`
A comprehensive API client that automatically handles JWT authentication for all requests to the Back-Talk backend.

**Features:**
- Automatic JWT token inclusion in request headers
- Token storage in localStorage
- Automatic redirect to login on 401 errors
- Support for all HTTP methods (GET, POST, PUT, DELETE, PATCH)
- Built-in authentication methods (login, validate, refresh, logout)
- JWT payload decoding for organization tracking
- Organization setup detection

**Usage:**
```typescript
import { apiClient } from '@app/lib/api-client';

// The client automatically includes JWT tokens
const agents = await apiClient.get('/api/v1/agent');
const newAgent = await apiClient.post('/api/v1/agent', { name: 'New Agent' });

// Check organization status
const hasOrg = apiClient.hasOrganization();
const orgId = apiClient.getOrganizationId();
```

### `use-auth.ts`
A React hook that provides authentication state and actions.

**Features:**
- User authentication state management
- Login/logout functionality
- Token validation and refresh
- Automatic token refresh (every 6 days)
- Organization setup tracking
- Error handling

**Usage:**
```typescript
import { useAuth } from '@app/lib/use-auth';

function MyComponent() {
  const { user, isAuthenticated, hasOrganization, organizationId, login, logout, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;
  if (!hasOrganization) return <div>Organization setup required</div>;

  return <div>Welcome, {user.username}! Org: {organizationId}</div>;
}
```

### `auth-guard.tsx`
A React component that protects routes requiring authentication.

**Features:**
- Automatic redirect to login page if not authenticated
- Loading state while checking authentication
- Higher-order component wrapper available

**Usage:**
```typescript
import { AuthGuard } from '@app/lib/auth-guard';

function ProtectedPage() {
  return (
    <AuthGuard>
      <div>This content is only visible to authenticated users</div>
    </AuthGuard>
  );
}

// Or as HOC
import { withAuthGuard } from '@app/lib/auth-guard';
const ProtectedComponent = withAuthGuard(MyComponent);
```

### `organization-guard.tsx`
A React component that checks if the user has an organization set up.

**Features:**
- Checks for organization_id in JWT payload
- Shows setup wizard prompt if organization is missing
- Loading state while checking organization status
- Higher-order component wrapper available

**Usage:**
```typescript
import { OrganizationGuard } from '@app/lib/organization-guard';

function OrganizationRequiredPage() {
  return (
    <OrganizationGuard>
      <div>This content requires an organization to be set up</div>
    </OrganizationGuard>
  );
}

// Or as HOC
import { withOrganizationGuard } from '@app/lib/organization-guard';
const OrgProtectedComponent = withOrganizationGuard(MyComponent);
```

## Authentication Flow

1. **Login (Web → App):**
   - User visits `https://back-talk.ai/login`
   - Enters credentials (username/email and password)
   - Frontend calls `POST /auth/login_check` on backend
   - Backend validates credentials and returns JWT token
   - Token is stored in localStorage
   - User is redirected to `https://app.back-talk.ai`

2. **App Authentication:**
   - App layout includes `AuthGuard` component
   - `AuthGuard` uses `useAuth` hook to check authentication
   - If token exists, validates it with backend
   - If valid, user can access app
   - If invalid/missing, redirects to login page

3. **API Requests:**
   - All API requests use `apiClient`
   - `apiClient` automatically includes JWT token in Authorization header
   - If request returns 401, token is cleared and user redirected to login

4. **Token Refresh:**
   - Tokens expire after 7 days
   - `useAuth` hook automatically refreshes tokens every 6 days
   - Manual refresh available via `refreshToken()` method

## Backend Endpoints

The backend provides these authentication endpoints:

- `POST /auth/login_check` - Login with username/password
- `POST /auth/validate` - Validate current token
- `POST /auth/refresh` - Refresh current token
- `POST /auth/logout` - Logout (client-side token removal)

## Environment Variables

Set these environment variables for proper configuration:

```env
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=https://api.back-talk.ai

# Backend (.env)
JWT_SECRET=your-secret-key-here
```

## Security Considerations

1. **JWT Secret:** Use a strong, unique secret key for JWT signing
2. **HTTPS:** Always use HTTPS in production
3. **Token Storage:** Tokens are stored in localStorage (consider httpOnly cookies for enhanced security)
4. **Token Expiration:** Tokens expire after 7 days
5. **CORS:** Backend includes CORS middleware for cross-origin requests

## Error Handling

The authentication system handles various error scenarios:

- **Invalid credentials:** Shows error message on login form
- **Expired tokens:** Automatically redirects to login
- **Network errors:** Shows error messages and maintains state
- **Invalid token format:** Clears token and redirects to login

## Testing

To test the authentication system:

1. Create a user in the backend database
2. Visit the login page and enter credentials
3. Check that you're redirected to the app
4. Verify that API calls include the JWT token
5. Test token refresh functionality
6. Test logout and re-authentication

## Troubleshooting

**Common Issues:**

1. **CORS errors:** Check backend CORS configuration
2. **Token not included:** Verify `apiClient` is being used for requests
3. **Redirect loops:** Check that login page doesn't include `AuthGuard`
4. **Token validation fails:** Verify JWT secret matches between frontend and backend

**Debug Tools:**

- Check browser localStorage for `auth_token`
- Use browser network tab to inspect API requests
- Check backend logs for authentication errors
- Use the UserProfile component to test authentication state
