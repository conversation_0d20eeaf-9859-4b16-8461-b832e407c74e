/**
 * API Client for Back-Talk App
 * Automatically includes <PERSON><PERSON><PERSON> token in all requests
 */

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: {
    id: string;
    username: string;
    verified: boolean;
    person: {
      firstName?: string;
      lastName?: string;
      email?: string;
    };
  };
  expires_in: number;
}

export interface ValidationResponse {
  valid: boolean;
  user?: {
    id: string;
    username: string;
    verified: boolean;
    person: {
      firstName?: string;
      lastName?: string;
      email?: string;
    };
  };
  expires_at?: number;
  message?: string;
}

export interface JWTPayload {
  iss: string;
  aud: string;
  iat: number;
  exp: number;
  user_id: string;
  username: string;
  verified: boolean;
  organization_id?: string;
}

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor() {
    // Use environment variable or default to API domain
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api.back-talk.ai';

    // Load token from localStorage or URL parameter on initialization
    if (typeof window !== 'undefined') {
      // First check if there's a token in the URL (from login redirect)
      const urlParams = new URLSearchParams(window.location.search);
      const urlToken = urlParams.get('token');

      if (urlToken) {
        // Store the token from URL and clean up the URL
        this.setToken(urlToken);
        // Remove token from URL for security
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('token');
        window.history.replaceState({}, '', newUrl.toString());
      } else {
        // Load existing token from localStorage
        this.token = localStorage.getItem('auth_token');
      }
    }
  }

  /**
   * Set the JWT token for authentication
   */
  setToken(token: string): void {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  /**
   * Get the current JWT token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * Clear the JWT token (logout)
   */
  clearToken(): void {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Decode JWT payload (client-side only, for reading claims)
   * Note: This does NOT validate the token - only decodes the payload
   */
  getTokenPayload(): JWTPayload | null {
    if (!this.token) return null;

    try {
      // JWT has 3 parts separated by dots: header.payload.signature
      const parts = this.token.split('.');
      if (parts.length !== 3) return null;

      // Decode the payload (base64url)
      const payload = parts[1];
      // Add padding if needed for base64 decoding
      const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
      const decodedPayload = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));

      return JSON.parse(decodedPayload) as JWTPayload;
    } catch (error) {
      console.error('Failed to decode JWT payload:', error);
      return null;
    }
  }

  /**
   * Check if user has an organization
   */
  hasOrganization(): boolean {
    const payload = this.getTokenPayload();
    return !!(payload?.organization_id);
  }

  /**
   * Get organization ID from token
   */
  getOrganizationId(): string | null {
    const payload = this.getTokenPayload();
    return payload?.organization_id || null;
  }

  /**
   * Make an authenticated request
   */
  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    const headers = new Headers(options.headers);
    headers.set('Content-Type', 'application/json');

    // Add JWT token if available
    if (this.token) {
        headers.set('Authorization', `Bearer ${this.token}`);
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);

      // Handle authentication errors
      if (response.status === 401) {
        this.clearToken();
        // Redirect to login page
        if (typeof window !== 'undefined') {
          const loginUrl = window.location.hostname === 'app.localhost'
            ? `http://localhost:${window.location.port || '9000'}/login`
            : 'https://back-talk.ai/login';
          window.location.href = loginUrl;
        }
        throw new Error('Authentication required');
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // Authentication specific methods

  /**
   * Login with username/email and password
   */
  async login(username: string, password: string): Promise<LoginResponse> {
    const response = await fetch(`${this.baseUrl}/auth/login_check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username, password }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || 'Login failed');
    }

    const data: LoginResponse = await response.json();

    if (data.success && data.token) {
      this.setToken(data.token);
    }

    return data;
  }

  /**
   * Validate current token
   */
  async validateToken(): Promise<ValidationResponse> {
    if (!this.token) {
      return { valid: false, message: 'No token available' };
    }

    try {
      const response = await fetch(`${this.baseUrl}/auth/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.token}`,
        },
      });

      const data: ValidationResponse = await response.json();

      if (!data.valid) {
        this.clearToken();
      }

      return data;
    } catch (error) {
      this.clearToken();
      return { valid: false, message: 'Token validation failed' };
    }
  }

  /**
   * Refresh the current token
   */
  async refreshToken(): Promise<{ success: boolean; token?: string }> {
    if (!this.token) {
      throw new Error('No token to refresh');
    }

    try {
      const response = await fetch(`${this.baseUrl}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.token}`,
        },
      });

      if (!response.ok) {
        this.clearToken();
        throw new Error('Token refresh failed');
      }

      const data = await response.json();

      if (data.success && data.token) {
        this.setToken(data.token);
      }

      return data;
    } catch (error) {
      this.clearToken();
      throw error;
    }
  }

  /**
   * Logout
   */
  async logout(): Promise<void> {
    try {
      if (this.token) {
        await this.post('/auth/logout');
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      this.clearToken();
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = 'https://back-talk.ai/login';
      }
    }
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for testing or custom instances
export default ApiClient;
