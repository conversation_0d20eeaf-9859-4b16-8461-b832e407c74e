"use client";

import { useEffect, ReactNode } from 'react';
import { useAuth } from './use-auth';

interface OrganizationGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * OrganizationGuard component that checks if user has an organization
 * Redirects to organization setup wizard if organization_id is missing from JWT
 */
export function OrganizationGuard({ children, fallback }: OrganizationGuardProps) {
  const { isAuthenticated, isLoading, hasOrganization, organizationId } = useAuth();

  useEffect(() => {
    // Only check organization after authentication is confirmed
    if (!isLoading && isAuthenticated && !hasOrganization) {
      // Redirect to organization setup wizard
      // TODO: Replace with actual wizard route when implemented
      console.log('User needs organization setup - redirecting to wizard');
      // window.location.href = '/app/setup/organization';
    }
  }, [isAuthenticated, isLoading, hasOrganization]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking organization setup...</p>
          </div>
        </div>
      )
    );
  }

  // Don't render if not authenticated (AuthGuard should handle this)
  if (!isAuthenticated) {
    return null;
  }

  // Show setup needed message if no organization
  if (!hasOrganization) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="mb-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Organization Setup Required</h2>
              <p className="text-gray-600 mb-6">
                To continue using Back-Talk, you need to set up your organization. This will only take a few minutes.
              </p>
              <button 
                onClick={() => {
                  // TODO: Navigate to organization setup wizard
                  console.log('Navigate to organization setup wizard');
                }}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Set Up Organization
              </button>
            </div>
            <div className="text-sm text-gray-500">
              <p>Organization ID: {organizationId || 'Not set'}</p>
            </div>
          </div>
        </div>
      )
    );
  }

  // Render children if user has organization
  return <>{children}</>;
}

/**
 * Higher-order component version of OrganizationGuard
 */
export function withOrganizationGuard<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function OrganizationGuardedComponent(props: P) {
    return (
      <OrganizationGuard fallback={fallback}>
        <Component {...props} />
      </OrganizationGuard>
    );
  };
}
