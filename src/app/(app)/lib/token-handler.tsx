"use client";

import { useEffect } from 'react';
import { apiClient } from './api-client';

/**
 * TokenHandler component that processes token from URL parameters
 * This should be included early in the app initialization
 */
export function TokenHandler() {
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') return;

    // Check for token in URL parameters (from login redirect)
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');

    if (urlToken) {
      console.log('Token received from login redirect');
      
      // Store the token
      apiClient.setToken(urlToken);
      
      // Clean up the URL by removing the token parameter
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('token');
      
      // Replace the current URL without the token (for security)
      window.history.replaceState({}, '', newUrl.toString());
      
      // Optional: Show a brief success message
      console.log('Login successful - token stored');
    }
  }, []);

  // This component doesn't render anything
  return null;
}

/**
 * Hook to handle token from URL parameters
 * Alternative to the component approach
 */
export function useTokenFromUrl() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');

    if (urlToken) {
      apiClient.setToken(urlToken);
      
      // Clean up URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('token');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, []);
}
