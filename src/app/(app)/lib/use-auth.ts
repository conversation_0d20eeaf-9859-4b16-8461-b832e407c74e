"use client";

import { useState, useEffect, useCallback } from 'react';
import { apiClient, type LoginResponse, type ValidationResponse } from './api-client';

export interface User {
  id: string;
  username: string;
  verified: boolean;
  person: {
    firstName?: string;
    lastName?: string;
    email?: string;
  };
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasOrganization: boolean;
  organizationId: string | null;
  error: string | null;
}

export interface AuthActions {
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  validateToken: () => Promise<boolean>;
  refreshToken: () => Promise<boolean>;
  clearError: () => void;
}

export function useAuth(): AuthState & AuthActions {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user && apiClient.isAuthenticated();
  const hasOrganization = apiClient.hasOrganization();
  const organizationId = apiClient.getOrganizationId();

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Validate the current token and set user if valid
   */
  const validateToken = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const result: ValidationResponse = await apiClient.validateToken();
      
      if (result.valid && result.user) {
        setUser(result.user);
        return true;
      } else {
        setUser(null);
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Token validation failed');
      setUser(null);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Login with username and password
   */
  const login = useCallback(async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const result: LoginResponse = await apiClient.login(username, password);
      
      if (result.success && result.user) {
        setUser(result.user);
        return true;
      } else {
        setError('Login failed');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh the current token
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      
      const result = await apiClient.refreshToken();
      
      if (result.success) {
        // Validate the new token to get updated user info
        return await validateToken();
      } else {
        setUser(null);
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Token refresh failed');
      setUser(null);
      return false;
    }
  }, [validateToken]);

  /**
   * Logout the user
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      await apiClient.logout();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Logout failed');
    } finally {
      setUser(null);
      setIsLoading(false);
    }
  }, []);

  /**
   * Initialize authentication state on mount
   */
  useEffect(() => {
    const initAuth = async () => {
      if (apiClient.isAuthenticated()) {
        await validateToken();
      } else {
        setIsLoading(false);
      }
    };

    initAuth();
  }, [validateToken]);

  /**
   * Set up token refresh interval
   */
  useEffect(() => {
    if (!isAuthenticated) return;

    // Refresh token every 6 days (token expires in 7 days)
    const refreshInterval = setInterval(() => {
      refreshToken();
    }, 6 * 24 * 60 * 60 * 1000); // 6 days in milliseconds

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated, refreshToken]);

  /**
   * Handle page visibility change to validate token when page becomes visible
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        validateToken();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated, validateToken]);

  return {
    user,
    isLoading,
    isAuthenticated,
    hasOrganization,
    organizationId,
    error,
    login,
    logout,
    validateToken,
    refreshToken,
    clearError,
  };
}
