interface AutoResponder {
    enabled: boolean,
    toneSample: string,
    responseDelay: string,
}

interface ForwardSequence {
    number: string,
    waitTime: number,
}

interface HumanDateTime {
    formatted: string,
    raw: {
        date: string,
        timezone_type: string,
        timezone: string,
    }
}

export default interface Agent {
    id: string,
    name: string,
    phone: string,
    status: string,
    forwardNumber: string | null,
    forwardSequence: ForwardSequence[],
    autoResponder: AutoResponder | null,
    createdAt: string,
    createdBy: string | null,
    updatedAt: string,
    updatedBy: string | null,
    metrics: {
        avgLeadResponseTime: number,
        leadResponseRate: number
        callsHandled: number,
        voicemailCount: number,
        conversationsHandled: number,
        conversionCount: number,
        conversionRate: number,
        avgConversionTime: number,
        avgCostPerConversion: number,
        avgLeadSentiment: number,
        totalMonthlyCost: number,
        messageCount: number,
        avgMessagePerConversation: number,
        smsReputation: number,
        lastActivity: HumanDateTime,
    },
    metadata: {
        createdAt: HumanDateTime,
        createdBy: string | null,
        updatedAt: HumanDateTime,
        updatedBy: string | null,
    }
}
