export default interface Conversation {
    id: string,
    lead: {
        id: string,
        person: {
            id: string,
            name: {
                full: string,
                first: string,
                last: string,
            }
        }
    },
    agent: {
        id: string,
        name: string,
    },
    leadPhone: string,
    lastMessage: string,
    lastMessageTime: string,
    messageCount: number,
    status: string,
}
