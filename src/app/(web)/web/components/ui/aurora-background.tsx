"use client"
import type React from "react"
import type { ReactNode } from "react"
import Footer from "@web/components/ui/footer";

interface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {
    children: ReactNode
    showRadialGradient?: boolean
}

function classNames(...classes: (string | undefined | null | false)[]): string {
    return classes.filter(Boolean).join(" ")
}

export const AuroraBackground = ({
                                     className,
                                     children,
                                     showRadialGradient = true,
                                     ...props
                                 }: AuroraBackgroundProps) => {
    return (
        <main>
            <div
                className={classNames(
                    // Make this a column that fills the screen and uses the aurora as page background
                    "transition-bg relative flex min-h-screen flex-col items-center justify-center bg-black text-white",
                    className,
                )}
                {...props}
            >
                <div className="absolute inset-0 overflow-hidden">
                    <div
                        className="absolute inset-0 animate-aurora opacity-50 blur-[10px]"
                        style={{
                            background: `
                repeating-linear-gradient(100deg, #000 0%, #000 7%, transparent 10%, transparent 12%, #000 16%),
                repeating-linear-gradient(100deg, var(--color-aurora-1) 10%, var(--color-aurora-2) 15%, var(--color-aurora-3) 20%, var(--color-aurora-4) 25%, var(--color-aurora-5) 30%)
              `,
                            backgroundSize: "300% 300%, 200% 200%",
                            backgroundPosition: "50% 0%, 50% 0%",
                        }}
                    />
                    <div
                        className="absolute inset-0 animate-aurora opacity-30 blur-[20px]"
                        style={{
                            background: `
                repeating-linear-gradient(100deg, #000 0%, #000 7%, transparent 10%, transparent 12%, #000 16%),
                repeating-linear-gradient(100deg, var(--color-aurora-1) 10%, var(--color-aurora-2) 15%, var(--color-aurora-3) 20%, var(--color-aurora-4) 25%, var(--color-aurora-5) 30%)
              `,
                            backgroundSize: "200% 200%, 100% 100%",
                            backgroundPosition: "50% 0%, 50% 0%",
                            animationDelay: "-30s",
                        }}
                    />

                    {showRadialGradient && (
                        <div
                            className="absolute inset-0"
                            style={{
                                background: "radial-gradient(ellipse at 50% 0%, black 10%, transparent 70%)",
                                mixBlendMode: "multiply",
                            }}
                        />
                    )}
                </div>
                <div className="z-10 flex-1 w-full flex flex-col items-center justify-center">
                    {children}
                </div>
                <Footer />
            </div>
        </main>
    )
}
