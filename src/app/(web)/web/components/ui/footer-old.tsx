import Link from "next/link"
import Image from "next/image"

export default function FooterOld() {
  return (
    <footer className="mt-auto z-50 bg-black">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-3">
            <Image src="/logo.svg" alt="Back-talk Logo" width={24} height={24} className="w-6 h-6" />
            <span className="text-white font-semibold">back-talk</span>
          </div>

          {/* Sub Navigation */}
          <nav className="flex flex-wrap justify-center gap-6 text-sm">
            <Link href="/features" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              Features
            </Link>
            <Link href="/pricing" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              Pricing
            </Link>
            <Link href="/contact" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              Contact
            </Link>
            <Link href="/faq" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              FAQ
            </Link>
            <Link href="/privacy" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
              Terms
            </Link>
          </nav>

          {/* Copyright */}
          <div className="text-neutral-400 text-sm">© 2025 Back-Talk. All rights reserved.</div>
        </div>
      </div>
    </footer>
  )
}
