import * as React from "react";
import Raw<PERSON>ogo from "@/assets/logo.svg";

type LogoProps = {
    primary?: string;   // e.g. "#fff" or "oklch(…)" or "hsl(var(--primary))"
    secondary?: string; // same
    className?: string; // size, layout via Tailwind
    style?: React.CSSProperties;
};

export default function Logo({
                                 primary,
                                 secondary,
                                 className,
                                 style,
                             }: LogoProps) {
    return (
        <span
            className={className}
            style={{
                // Pass CSS variables straight to the inline SVG
                ...(primary ? { ["--logo-primary" as any]: primary } : null),
                ...(secondary ? { ["--logo-secondary" as any]: secondary } : null),
                ...style,
            }}
        >
      <RawLogo aria-label="Back-Talk logo" />
    </span>
    );
}
