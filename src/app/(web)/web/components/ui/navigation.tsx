"use client"

import type React from "react"

import {useState} from "react"
import Link from "next/link";
import Logo from "@/assets/logo.svg";

export default function Navigation() {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

    return (
        <nav className="fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-sm border-b border-white/10">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-16">
                    <div className="flex-shrink-0">
                        <Link href="/">
                            <div className="flex items-center space-x-3">
                                <Logo aria-label="Back-Talk logo" width={32} height={32} style={{ "--logo-primary": "white", "--logo-secondary": "var(--color-primary-300)" }} className="w-8 h-8" />
                                <span className="text-white text-xl font-bold">back-<span className="text-primary-300">talk</span></span>
                            </div>
                        </Link>
                    </div>

                    {/* Navigation Links */}
                    <div className="hidden md:block">
                        <div className="ml-10 flex items-baseline space-x-8">
                            <Link
                                href="/"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                Home
                            </Link>
                            <Link
                                href="/features"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                Features
                            </Link>
                            <Link
                                href="/pricing"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                Pricing
                            </Link>
                            <Link
                                href="/faq"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                FAQ
                            </Link>
                            <Link
                                href="/contact"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                Contact
                            </Link>
                            <Link
                                href="/login"
                                className="text-white transition-colors px-3 py-2 text-sm font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                            >
                                Login / Signup
                            </Link>
                        </div>
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <button
                            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                            className="text-white transition-colors"
                            style={
                                {"--tw-text-opacity": "1", color: "rgb(255 255 255 / var(--tw-text-opacity))"} as React.CSSProperties
                            }
                            onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                            onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                        >
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                        </button>
                    </div>
                </div>

                {isMobileMenuOpen && (
                    <div className="md:hidden">
                        <div className="px-2 pt-2 pb-3 space-y-1 bg-black/90 backdrop-blur-sm border-t border-white/10">
                            <Link
                                href="/public"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Home
                            </Link>
                            <Link
                                href="/features"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Features
                            </Link>
                            <Link
                                href="/pricing"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Pricing
                            </Link>
                            <Link
                                href="/faq"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                FAQ
                            </Link>
                            <Link
                                href="/contact"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Contact
                            </Link>
                            <Link
                                href="/login"
                                className="text-white transition-colors block px-3 py-2 text-base font-medium"
                                style={
                                    {
                                        "--tw-text-opacity": "1",
                                        color: "rgb(255 255 255 / var(--tw-text-opacity))",
                                    } as React.CSSProperties
                                }
                                onMouseEnter={(e) => (e.currentTarget.style.color = "var(--color-primary-300)")}
                                onMouseLeave={(e) => (e.currentTarget.style.color = "rgb(255 255 255)")}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                Login / Signup
                            </Link>
                        </div>
                    </div>
                )}
            </div>
        </nav>
    )
}
