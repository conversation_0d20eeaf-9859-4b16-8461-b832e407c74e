import Link from "next/link"
import Image from "next/image";
import Logo from "@/assets/logo.svg";
import type React from "react";

export default function Footer() {
  return (
      <footer className="mt-auto z-50 w-full opacity-75">
          <div className="mx-auto px-4 py-8">
              <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                  <div className="flex-shrink-0  hidden md:flex">
                      <Link href="/">
                          <div className="flex items-center space-x-3">
                              <Logo aria-label="Back-Talk logo" width={20} height={20} style={{ "--logo-primary": "white", "--logo-secondary": "var(--color-primary-300)" }} />
                              <span className="text-white text-xs">back-<span className="text-primary-300">talk</span></span>
                          </div>
                      </Link>
                  </div>

                  {/* Sub Navigation */}
                  <nav className="flex-wrap justify-center gap-6 text-xs hidden md:flex">
                      <Link href="/features" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          Features
                      </Link>
                      <Link href="/pricing" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          Pricing
                      </Link>
                      <Link href="/contact" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          Contact
                      </Link>
                      <Link href="/faq" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          FAQ
                      </Link>
                      <Link href="/privacy" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          Privacy
                      </Link>
                      <Link href="/terms" className="text-neutral-300 hover:text-[var(--primary-300)] transition-colors">
                          Terms
                      </Link>
                  </nav>

                  {/* Copyright */}
                  <div className="text-neutral-400 text-xs">© 2025 Back-Talk. All rights reserved.</div>
              </div>
          </div>
      </footer>
  )
}
