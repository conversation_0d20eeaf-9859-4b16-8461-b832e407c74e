import Link from "next/link"
import Logo from "@/assets/logo.svg";
import type React from "react";

export default function Page() {
    return (
        <div className="relative flex flex-col gap-4 items-center justify-center px-4">
            <div className="flex items-center flex-wrap justify-center space-x-3">
                <Logo aria-label="Back-Talk logo" width={200} height={200} style={{ "--logo-primary": "white", "--logo-secondary": "var(--color-primary-300)" }} />
                <div className="text-5xl md:text-9xl font-bold text-white text-center text-balance">back-<span className="text-primary-300">talk</span></div>
            </div>
            <div className="font-extralight text-base md:text-4xl text-neutral-200 py-4 text-center text-pretty">
                Never lose another lead to voicemail again.
            </div>
            <Link
                href="/signup"
                className="bg-primary-600 hover:bg-primary-700 rounded-full w-fit text-white px-6 py-3 font-medium transition-colors"
            >
                Sign Up Now
            </Link>
        </div>
    )
}
