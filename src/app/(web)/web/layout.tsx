import type React from "react"
import type { Metada<PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "@/globals.css";
import { getCSSVariables } from "@/lib/colors"
import { Suspense } from "react"
import Navigation from "@web/components/ui/navigation";
import {AuroraBackground} from "@web/components/ui/aurora-background";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Back-Talk",
  description: "Never lose another lead to voicemail again",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
    <head>
        <link rel="icon" href="/favicon.svg" />
        <link
            rel="apple-touch-icon"
            href="/favicon.svg"
            type="image/svg+xml"
            sizes="any"
        />
    </head>
      <body
        className={`font-sans ${geistSans.variable} ${geistMono.variable} antialiased flex flex-col min-h-screen`}
        style={getCSSVariables()}
      >
      <Navigation />
      <AuroraBackground>
          <Suspense fallback={null}>{children}</Suspense>
      </AuroraBackground>
      </body>
    </html>
  )
}
