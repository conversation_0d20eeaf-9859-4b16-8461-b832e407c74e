import Link from "next/link";

export default function FeaturesPage() {
    const features = [
        {
            title: "Missed-Call → Text",
            description:
                "Automatically text back callers within seconds with a friendly message that keeps the conversation going and sets expectations for a callback.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
                </svg>
            ),
        },
        {
            title: "Appointment Q&A",
            description:
                "Answers common questions—availability, prep, policies, pricing ranges—so customers feel confident to book.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-6h2v6zm0-8h-2V7h2v4z"/>
                </svg>
            ),
        },
        {
            title: "Calendar Integration",
            description:
                "Connect Google, Outlook, or iCal to let Back-Talk propose times, confirm appointments, and write clean calendar invites.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                </svg>
            ),
        },
        {
            title: "Lead Capture",
            description:
                "Collect name, service interest, and best callback time. Get instant notifications via SMS and email, plus a daily summary.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
            ),
        },
        {
            title: "Tone-Matched Replies",
            description:
                "We tailor language to your brand—professional, friendly, or casual—so it feels like you, even when you're on a job.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
            ),
        },
        {
            title: "Compliant & Secure",
            description:
                "Opt-in/opt-out support, conversation logging, and encryption keep you covered while you focus on the work.",
            icon: (
                <svg className="w-8 h-8 text-primary-200" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                </svg>
            ),
        },
    ]

    return (
        <div className="relative flex flex-col items-center justify-center px-4 py-16">
            <div className="text-4xl md:text-6xl font-bold text-white text-center mb-4">Features</div>
            <div className="font-extralight text-lg md:text-2xl text-neutral-200 text-center mb-16 max-w-3xl">
                Powerful AI-driven features that help you never miss a lead and keep your customers engaged.
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl w-full">
                {features.map((feature, index) => (
                    <div
                        key={index}
                        className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300 hover:scale-105"
                    >
                        <div className="mb-4">{feature.icon}</div>
                        <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                        <p className="text-neutral-200 leading-relaxed">{feature.description}</p>
                    </div>
                ))}
            </div>

            <div className="mt-16">
                <Link href="/signup" className="bg-white rounded-full w-fit text-black px-8 py-3 hover:bg-gray-200 transition-colors font-medium">
                    Get Started Today
                </Link>
            </div>
        </div>
    )
}
