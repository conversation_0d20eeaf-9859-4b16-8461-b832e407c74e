"use client"

import Link from "next/link";

export default function PricingPage() {
    const plans = [
        {
            name: "<PERSON>",
            price: "$65",
            period: "/mo",
            description: "Perfect for solo operators getting started.",
            features: [
                "Up to 350 messages / mo",
                "Missed-call auto-text",
                "Basic Q&A + lead capture",
                "Email & SMS notifications",
            ],
            popular: false,
        },
        {
            name: "Pro",
            price: "$95",
            period: "/mo",
            description: "For busy small teams that need scheduling.",
            features: [
                "Up to 750 messages / mo",
                "Advanced appointment Q&A",
                "Google/Outlook calendar integration",
                "Tone customization & templates",
            ],
            popular: true,
        },
        {
            name: "Business",
            price: "Custom",
            period: "",
            description: "High-volume, multi-location, or special workflows.",
            features: [
                "Unlimited messages (fair use)",
                "Multiple numbers & locations",
                "Custom integrations & SLAs",
                "Dedicated support",
            ],
            popular: false,
        },
    ]

    return (
        <div className="relative flex flex-col items-center justify-center px-4 py-16">
            <div className="text-4xl md:text-6xl font-bold text-white text-center mb-4">Pricing</div>
            <div className="font-extralight text-lg md:text-2xl text-neutral-200 text-center mb-16 max-w-3xl">
                Choose the perfect plan for your business needs
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl w-full">
                {plans.map((plan, index) => (
                    <div
                        key={index}
                        className={`bg-white/10 backdrop-blur-md rounded-2xl p-8 border transition-all duration-300 hover:scale-105 relative ${
                            plan.popular ? "border-primary-400 bg-white/15" : "border-white/20 hover:bg-white/15"
                        }`}
                    >
                        {plan.popular && (
                            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                            </div>
                        )}

                        <div className="text-center mb-6">
                            <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                            <div className="flex items-baseline justify-center mb-2">
                                <span className="text-4xl font-bold text-white">{plan.price}</span>
                                <span className="text-lg text-neutral-300">{plan.period}</span>
                            </div>
                            <p className="text-neutral-200">{plan.description}</p>
                        </div>

                        <ul className="space-y-3 mb-8 min-h-[200px]">
                            {plan.features.map((feature, featureIndex) => (
                                <li key={featureIndex} className="flex items-start">
                                    <svg
                                        className="w-5 h-5 text-primary-400 mr-3 mt-0.5 flex-shrink-0"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                    </svg>
                                    <span className="text-neutral-200">{feature}</span>
                                </li>
                            ))}
                        </ul>

                        <Link href={plan.name === "Business" ? "/contact" : "/signup"}
                            className={`w-full py-3 px-6 rounded-full font-medium transition-colors block text-center ${
                                plan.popular
                                    ? "bg-primary-500 text-white hover:bg-primary-600"
                                    : "bg-white text-black hover:bg-gray-200"
                            }`}
                        >
                            {plan.name === "Business" ? "Contact Sales" : "Get Started"}
                        </Link>
                    </div>
                ))}
            </div>

        </div>
    )
}
