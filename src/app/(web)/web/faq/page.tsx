"use client"

import {useState} from "react"
import Link from "next/link";

export default function FAQPage() {
    const [openIndex, setOpenIndex] = useState<number | null>(null)

    const faqs = [
        {
            question: "Will Back-Talk replace my phone provider?",
            answer:
                "No. Back-Talk works alongside your current provider. We trigger SMS after a missed call and continue the conversation there.",
        },
        {
            question: "Can it schedule into my calendar?",
            answer:
                "Yes. Connect Google, Outlook, or iCal and Back-Talk can propose times, book, and send confirmations automatically.",
        },
        {
            question: "What about my brand voice?",
            answer:
                "We tune replies to match your tone (friendly, formal, casual). You can approve sample scripts before going live.",
        },
        {
            question: "Is this compliant with texting rules?",
            answer:
                "Yes. We include opt-in/out handling, quiet hours, and auditing. Customers can stop messages at any time.",
        },
    ]

    const toggleFAQ = (index: number) => {
        setOpenIndex(openIndex === index ? null : index)
    }

    return (
        <div className="relative flex flex-col items-center justify-center px-4 py-16">
            <div className="text-4xl md:text-6xl font-bold text-white text-center mb-4">FAQ</div>
            <div className="font-extralight text-lg md:text-2xl text-neutral-200 text-center mb-16 max-w-3xl">
                Frequently asked questions about Back-Talk AI
            </div>

            <div className="max-w-4xl w-full space-y-4">
                {faqs.map((faq, index) => (
                    <div
                        key={index}
                        className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden"
                    >
                        <button
                            onClick={() => toggleFAQ(index)}
                            className="w-full px-6 py-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                        >
                            <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                            <svg
                                className={`w-6 h-6 text-primary-300 transform transition-transform ${
                                    openIndex === index ? "rotate-180" : ""
                                }`}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        {openIndex === index && (
                            <div className="px-6 pb-6">
                                <p className="text-neutral-200 leading-relaxed">{faq.answer}</p>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            <div className="mt-16 text-center">
                <p className="text-neutral-300 mb-4">Still have questions?</p>
                <Link href="/contact" className="bg-white rounded-full text-black px-8 py-3 hover:bg-gray-200 transition-colors font-medium">
                    Contact Support
                </Link>
            </div>
        </div>
    )
}
