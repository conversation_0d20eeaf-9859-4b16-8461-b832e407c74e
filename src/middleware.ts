// middleware.ts
import { NextRequest, NextResponse } from "next/server";

const WEB_PREFIX = "/web";
const APP_PREFIX = "/app";
const API_PREFIX = "/api";

// Production hosts
const WEB_HOST = "back-talk.ai";
const APP_HOST = "app.back-talk.ai";
const API_HOST = "api.back-talk.ai";

// Dev helpers: app.localhost and api.localhost also work and resolve to 127.0.0.1
function isWebHost(host: string) {
    return (
        host === WEB_HOST ||
        host === "localhost" || // when opening http://localhost:<port>, show the web surface
        host.endsWith(".localhost") === false // any unknown host falls back to web
    );
}

function isAppHost(host: string) {
    return host === APP_HOST || host === "app.localhost";
}

function isApiHost(host: string) {
    return host === API_HOST || host === "api.localhost";
}

export async function middleware(req: NextRequest) {
    const url = req.nextUrl.clone()
    const { pathname } = url
    const host = req.headers.get("host") || ""

    // Bypass middleware for Next internals and static/public files
    // This prevents rewriting assets like /_next/static/... and any direct file (e.g., *.js, *.css, *.woff2)
    if (
        pathname.startsWith("/_next") ||
        pathname.startsWith("/static") ||
        pathname.startsWith("/images") ||
        pathname.startsWith("/assets") ||
        pathname === "/favicon.ico" ||
        pathname === "/favicon.svg" ||
        pathname === "/robots.txt" ||
        pathname === "/sitemap.xml" ||
        pathname.match(/\.[a-zA-Z0-9]+$/) // any file with an extension
    ) {
        return NextResponse.next()
    }

    // API host → internal /api prefix
    if (isApiHost(host)) {
        // Prepend /api to all non-prefixed paths
        if (!pathname.startsWith(API_PREFIX)) {
            url.pathname = API_PREFIX + pathname;
            return NextResponse.rewrite(url);
        }

        // Basic CORS: allow the app origin
        const appOrigin =
            host.endsWith(".localhost")
                ? `http://app.localhost:${url.port || "9000"}`
                : `https://${APP_HOST}`;

        if (req.method === "OPTIONS") {
            return new NextResponse(null, {
                status: 204,
                headers: {
                    "Access-Control-Allow-Origin": appOrigin,
                    "Access-Control-Allow-Methods": "GET,POST,PUT,PATCH,DELETE,OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization",
                    "Access-Control-Allow-Credentials": "true",
                    Vary: "Origin",
                },
            });
        }

        const res = NextResponse.next();
        res.headers.set("Access-Control-Allow-Origin", appOrigin);
        res.headers.set("Access-Control-Allow-Credentials", "true");
        res.headers.append("Vary", "Origin");
        return res;
    }

    // App host → internal /app prefix
    if (isAppHost(host)) {
        if (!pathname.startsWith(APP_PREFIX)) {
            url.pathname = APP_PREFIX + pathname;
            return NextResponse.rewrite(url);
        }

        // Optional coarse auth gate:
        // const session = await getSession(req);
        // if (!session) {
        //   url.pathname = "/auth/sign-in";
        //   url.searchParams.set("redirect", pathname.replace(APP_PREFIX, "") || "/");
        //   return NextResponse.redirect(url);
        // }

        return NextResponse.next();
    }

    // Web host (default) → internal /web prefix
    if (isWebHost(host) && !pathname.startsWith(WEB_PREFIX)) {
        url.pathname = WEB_PREFIX + pathname;
        return NextResponse.rewrite(url);
    }

    return NextResponse.next();
}

export const config = {
    // Exclude Next internals and most static assets from ever hitting the middleware
    matcher: [
        "/((?!_next|static|images|assets|favicon.ico|favicon.svg|robots.txt|sitemap.xml|.*\\..*).*)",
    ],
}
