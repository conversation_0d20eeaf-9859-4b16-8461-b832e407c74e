import type React from "react"

/**
 * Color utilities and palette generation
 * - Produces a primary scale (200..800)
 * - Produces an "aurora-analogous" set of SIX colors:
 *   3 shades of the primary hue + 1 from first analogous hue + 2 from second analogous hue
 *   (If you want 4 primary shades instead, set PRIMARY_SHADE_COUNT = 4 and you'll get 7 total.)
 */

export interface RGB {
    r: number;
    g: number;
    b: number
}

interface ColorConfig {
    primary: RGB
}

// Default purple color (104, 14, 184) = #680EB8
export const defaultConfig: ColorConfig = {
    primary: {
        r: 104,
        g: 14,
        b: 184
    },
}

/* ---------- Math helpers ---------- */
const clamp01 = (x: number) => Math.max(0, Math.min(1, x))
const normHue = (h: number) => {
    const x = h % 360
    return x < 0 ? x + 360 : x
}

function rgbToHsl({r, g, b}: RGB): { h: number; s: number; l: number } {
    const R = clamp01(r / 255), G = clamp01(g / 255), B = clamp01(b / 255)
    const max = Math.max(R, G, B), min = Math.min(R, G, B)
    const d = max - min

    let h = 0
    if (d !== 0) {
        if (max === R) h = 60 * (((G - B) / d) % 6)
        else if (max === G) h = 60 * (((B - R) / d) + 2)
        else h = 60 * (((R - G) / d) + 4)
    }
    if (h < 0) h += 360

    const l = (max + min) / 2
    const s = d === 0 ? 0 : d / (1 - Math.abs(2 * l - 1))
    return {h, s, l}
}

function hslToRgb(h: number, s: number, l: number): RGB {
    h = normHue(h);
    s = clamp01(s);
    l = clamp01(l)
    const c = (1 - Math.abs(2 * l - 1)) * s
    const x = c * (1 - Math.abs(((h / 60) % 2) - 1))
    const m = l - c / 2

    let rP = 0, gP = 0, bP = 0
    if (h < 60) {
        rP = c;
        gP = x;
        bP = 0
    } else if (h < 120) {
        rP = x;
        gP = c;
        bP = 0
    } else if (h < 180) {
        rP = 0;
        gP = c;
        bP = x
    } else if (h < 240) {
        rP = 0;
        gP = x;
        bP = c
    } else if (h < 300) {
        rP = x;
        gP = 0;
        bP = c
    } else {
        rP = c;
        gP = 0;
        bP = x
    }

    return {
        r: Math.round((rP + m) * 255),
        g: Math.round((gP + m) * 255),
        b: Math.round((bP + m) * 255),
    }
}

const toHex = (n: number) => n.toString(16).padStart(2, "0")

function hslToHex(h: number, s: number, l: number): string {
    const {r, g, b} = hslToRgb(h, s, l)
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/* ---------- Core palette generation ---------- */

export type GeneratedColors = {
    // Primary scale
    primary200: string
    primary300: string
    primary400: string
    primary500: string
    primary600: string
    primary700: string
    primary800: string
    // Secondary scale
    secondary200: string
    secondary300: string
    secondary400: string
    secondary500: string
    secondary600: string
    secondary700: string
    secondary800: string
    // Aurora-analogous six
    aurora1: string
    aurora2: string
    aurora3: string
    aurora4: string
    aurora5: string
    aurora6: string
}

function lightenDarken({r, g, b}: RGB, factor: number): RGB {
    // factor > 1 => lighten; factor < 1 => darken
    if (factor > 1) {
        const amt = factor - 1
        return {
            r: Math.min(255, Math.round(r + (255 - r) * amt)),
            g: Math.min(255, Math.round(g + (255 - g) * amt)),
            b: Math.min(255, Math.round(b + (255 - b) * amt)),
        }
    } else {
        return {
            r: Math.round(r * factor),
            g: Math.round(g * factor),
            b: Math.round(b * factor),
        }
    }
}

function rgbToHex({r, g, b}: RGB): string {
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * Build a 200..800 scale around the primary using simple brightness factors.
 * You can tune these to match your brand.
 */
function buildPrimaryScale(primary: RGB) {
    const factors = {
        200: 1.35,
        300: 1.18,
        400: 1.07,
        500: 1.00,
        600: 0.88,
        700: 0.78,
        800: 0.68,
    } as const

    return {
        primary200: rgbToHex(lightenDarken(primary, factors[200])),
        primary300: rgbToHex(lightenDarken(primary, factors[300])),
        primary400: rgbToHex(lightenDarken(primary, factors[400])),
        primary500: rgbToHex(lightenDarken(primary, factors[500])),
        primary600: rgbToHex(lightenDarken(primary, factors[600])),
        primary700: rgbToHex(lightenDarken(primary, factors[700])),
        primary800: rgbToHex(lightenDarken(primary, factors[800])),
    }
}

/* ---------- Secondary color + scale ---------- */

export type SecondaryScale = {
    secondary200: string
    secondary300: string
    secondary400: string
    secondary500: string
    secondary600: string
    secondary700: string
    secondary800: string
}

type SecondaryOptions = {
    // +150 = split-complement clockwise, -150 = counter-clockwise
    // If you want to flip the feel (warmer vs cooler), change the sign.
    splitOffsetDeg?: number   // default +150
    satFactor?: number        // default 0.75  (slightly desaturate)
    lightnessTarget?: number  // default 0.56  (comfortable mid-lightness)
    lightnessBlend?: number   // default 0.30  (how much we blend toward target)
}

/** Derive a soft secondary from a primary using a split-complement neighbor. */
function deriveSecondaryFromPrimary(primary: RGB, opts?: SecondaryOptions): RGB {
    const {
        splitOffsetDeg = 150,
        satFactor = 0.75,
        lightnessTarget = 0.56,
        lightnessBlend = 0.30,
    } = opts ?? {}

    const { h, s, l } = rgbToHsl(primary)

    // Choose a split-complementary hue (complement ±30° => 180° ± 30° => ±150° from primary)
    const h2 = normHue(h + splitOffsetDeg)

    // Ease saturation down for calmer UIs, and pull lightness toward a comfy mid value.
    const s2 = clamp01(s * satFactor)
    const l2 = clamp01(lightnessTarget + (l - 0.5) * lightnessBlend)

    return hslToRgb(h2, s2, l2)
}

/**
 * Build a 200..800 scale around the derived secondary color.
 * Uses the exact same brightness factors as buildPrimaryScale().
 */
export function buildSecondaryScale(primary: RGB, opts?: SecondaryOptions): SecondaryScale {
    const secondary = deriveSecondaryFromPrimary(primary, opts)

    const factors = {
        200: 1.35,
        300: 1.18,
        400: 1.07,
        500: 1.00,
        600: 0.88,
        700: 0.78,
        800: 0.68,
    } as const

    return {
        secondary200: rgbToHex(lightenDarken(secondary, factors[200])),
        secondary300: rgbToHex(lightenDarken(secondary, factors[300])),
        secondary400: rgbToHex(lightenDarken(secondary, factors[400])),
        secondary500: rgbToHex(lightenDarken(secondary, factors[500])),
        secondary600: rgbToHex(lightenDarken(secondary, factors[600])),
        secondary700: rgbToHex(lightenDarken(secondary, factors[700])),
        secondary800: rgbToHex(lightenDarken(secondary, factors[800])),
    }
}

/**
 * Generate SIX "aurora-analogous" colors:
 * - First 3: primary shades (H same, L varies) with a small saturation lift
 * - Then: 1 color from first analogous hue (H + offset)
 * - Then: 2 colors from second analogous hue (H - offset), dark & light
 *
 * To switch to 4 primary shades (and thus 7 total), change PRIMARY_SHADE_COUNT to 4.
 */
export function generateAuroraAnalogous(primary: RGB, options?: {
    analogousOffsetDeg?: number
    PRIMARY_SHADE_COUNT?: number // default 3 -> total 6
}): string[] {
    const offset = options?.analogousOffsetDeg ?? 30
    const PRIMARY_SHADE_COUNT = options?.PRIMARY_SHADE_COUNT ?? 3

    const {h, s, l} = rgbToHsl(primary)
    const sAurora = clamp01(s * 1.05)

    // Choose lightness stops for primary variants
    const stopsBase = [l * 0.72, l, 0.9 - (0.9 - l) * 0.45, 0.96]
    const primaryStops = stopsBase.slice(0, Math.max(1, Math.min(4, PRIMARY_SHADE_COUNT)))

    const firstAnalogH = normHue(h + offset)
    const secondAnalogH = normHue(h - offset)

    const primaryShades = primaryStops.map(L => hslToHex(h, sAurora, clamp01(L)))
    const firstAnalog = hslToHex(firstAnalogH, sAurora, clamp01(l * 0.92 + 0.04))
    const secondAnalogDark = hslToHex(secondAnalogH, sAurora, clamp01(l * 0.68 + 0.02))
    const secondAnalogLight = hslToHex(secondAnalogH, sAurora, clamp01(l * 0.88 + 0.05))

    const out = [...primaryShades, firstAnalog, secondAnalogDark, secondAnalogLight]
    // If we somehow produced more than 6, trim; if less, pad by repeating the last
    if (out.length > 6) return out.slice(0, 6)
    while (out.length < 6) out.push(out[out.length - 1])
    return out
}

/** Generate the full color object (primary scale + aurora set) */
export function generateColors(cfg: ColorConfig = defaultConfig): GeneratedColors {
    const primary = cfg.primary
    const scale = buildPrimaryScale(primary)
    const secondaryScale = buildSecondaryScale(primary)

    const aurora = generateAuroraAnalogous(primary) // 6 hex strings
    return {
        ...scale,
        ...secondaryScale,
        aurora1: aurora[0],
        aurora2: aurora[1],
        aurora3: aurora[2],
        aurora4: aurora[3],
        aurora5: aurora[4],
        aurora6: aurora[5],
    }
}

/** Export CSS variables (use on a container/root) */
export function getCSSVariables(cfg: ColorConfig = defaultConfig): React.CSSProperties {
    const colors = generateColors(cfg)
    return {
        "--color-primary-200": colors.primary200,
        "--color-primary-300": colors.primary300,
        "--color-primary-400": colors.primary400,
        "--color-primary-500": colors.primary500,
        "--color-primary-600": colors.primary600,
        "--color-primary-700": colors.primary700,
        "--color-primary-800": colors.primary800,
        "--color-secondary-200": colors.secondary200,
        "--color-secondary-300": colors.secondary300,
        "--color-secondary-400": colors.secondary400,
        "--color-secondary-500": colors.secondary500,
        "--color-secondary-600": colors.secondary600,
        "--color-secondary-700": colors.secondary700,
        "--color-secondary-800": colors.secondary800,
        "--color-aurora-1": colors.aurora1,
        "--color-aurora-2": colors.aurora2,
        "--color-aurora-3": colors.aurora3,
        "--color-aurora-4": colors.aurora4,
        "--color-aurora-5": colors.aurora5,
        "--color-aurora-6": colors.aurora6,
    } as React.CSSProperties
}

// Convenience default export for common usage
const Colors = {defaultConfig, generateColors, getCSSVariables, generateAuroraAnalogous}
export default Colors
