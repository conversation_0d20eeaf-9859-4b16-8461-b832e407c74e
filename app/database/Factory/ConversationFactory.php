<?php
namespace Database\Factory;

use App\Database\Entity\Conversation;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Enum\ConversationStatus;
use App\Database\Enum\ConversationPriority;
use Doctrine\Common\Collections\ArrayCollection;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class ConversationFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Conversation::class;
    }

    public function makeEntity(array $definition): Conversation
    {
        $conversation = new Conversation();
        $conversation->status = $definition['status'];
        $conversation->priority = $definition['priority'];
        $conversation->isAgentActive = $definition['isAgentActive'];

        // Set relationships if provided
        if (isset($definition['lead'])) {
            $conversation->lead = $definition['lead'];
        }
        if (isset($definition['agent'])) {
            $conversation->agent = $definition['agent'];
        }

        return $conversation;
    }

    public function definition(): array
    {
        $statuses = [
            ConversationStatus::ACTIVE,
            ConversationStatus::PENDING,
            ConversationStatus::RESOLVED,
            ConversationStatus::ARCHIVED,
        ];

        $priorities = [
            ConversationPriority::HIGH,
            ConversationPriority::MEDIUM,
            ConversationPriority::LOW,
        ];

        return [
            'status' => $this->faker->randomElement($statuses),
            'priority' => $this->faker->randomElement($priorities),
            'isAgentActive' => $this->faker->boolean(80), // 80% chance agent is active
        ];
    }

    public function withLead(Lead $lead): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['lead' => $lead]);
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }

    public function withNotes(array $notes): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['notes' => new ArrayCollection($notes)]);
    }
}
