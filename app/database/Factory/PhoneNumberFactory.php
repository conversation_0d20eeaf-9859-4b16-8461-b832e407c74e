<?php
namespace Database\Factory;

use App\Database\Entity\PhoneNumber;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class PhoneNumberFactory extends AbstractFactory
{
    public function entity(): string
    {
        return PhoneNumber::class;
    }

    public function makeEntity(array $definition): PhoneNumber
    {
        $phoneNumber = new PhoneNumber();
        $phoneNumber->number = $definition['number'];
        $phoneNumber->type = $definition['type'];
        $phoneNumber->status = $definition['status'];
        $phoneNumber->monthlyRate = $definition['monthlyRate'] ?? null;
        $phoneNumber->purchaseDate = $definition['purchaseDate'] ?? null;

        return $phoneNumber;
    }

    public function definition(): array
    {
        $types = [
            PhoneNumberType::LANDLINE,
            PhoneNumberType::VIRTUAL,
            PhoneNumberType::MOBILE,
            PhoneNumberType::FAX,
        ];

        $statuses = [
            PhoneNumberStatus::ACTIVE,
            PhoneNumberStatus::INACTIVE,
            PhoneNumberStatus::PENDING,
        ];

        return [
            'number' => $this->faker->randomNumber(9, true) + 1000000000, // 10-digit phone number
            'type' => $this->faker->randomElement($types),
            'status' => $this->faker->randomElement($statuses),
            'monthlyRate' => $this->faker->optional(0.8)->randomFloat(2, 5.00, 50.00),
            'purchaseDate' => \DateTimeImmutable::createFromMutable($this->faker->dateTimeBetween('-2 years', 'now')),
        ];
    }
}
