<?php
namespace Database\Factory;

use App\Database\Entity\Person;
use App\Database\Entity\PhoneNumber;
use App\Database\Entity\Address;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class PersonFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Person::class;
    }

    public function makeEntity(array $definition): Person
    {
        $person = new Person();
        $person->firstName = $definition['firstName'] ?? null;
        $person->lastName = $definition['lastName'] ?? null;
        $person->email = $definition['email'] ?? null;

        // Set relationships if provided
        if (isset($definition['phoneNumber'])) {
            $person->phoneNumber = $definition['phoneNumber'];
        }
        if (isset($definition['address'])) {
            $person->address = $definition['address'];
        }

        return $person;
    }

    public function definition(): array
    {
        return [
            'firstName' => $this->faker->optional(0.9)->firstName(),
            'lastName' => $this->faker->optional(0.9)->lastName(),
            'email' => $this->faker->unique()->safeEmail(),
        ];
    }

    public function withPhoneNumber(?PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['phoneNumber' => $phoneNumber]);
    }

    public function withAddress(?Address $address): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['address' => $address]);
    }
}
