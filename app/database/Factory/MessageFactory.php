<?php
namespace Database\Factory;

use App\Database\Entity\Message;
use App\Database\Entity\Conversation;
use App\Database\Entity\User;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Enum\MessageSender;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class MessageFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Message::class;
    }

    public function makeEntity(array $definition): Message
    {
        $message = new Message();
        $message->content = $definition['content'];
        $message->sender = $definition['sender'];

        // Set relationships if provided
        if (isset($definition['conversation'])) {
            $message->conversation = $definition['conversation'];
        }
        if (isset($definition['author'])) {
            $message->author = $definition['author'];
        }
        if (isset($definition['lead'])) {
            $message->lead = $definition['lead'];
        }
        if (isset($definition['agent'])) {
            $message->agent = $definition['agent'];
        }

        return $message;
    }

    public function definition(): array
    {
        $senders = [
            MessageSender::LEAD,
            MessageSender::AGENT,
            MessageSender::USER,
        ];

        $leadMessages = [
            "Hi, I'm interested in your services.",
            "Can you tell me more about pricing?",
            "When would be a good time to schedule a consultation?",
            "I have some questions about the installation process.",
            "What's included in the maintenance package?",
            "How long does the typical project take?",
            "Do you offer financing options?",
        ];

        $agentMessages = [
            "Thank you for your interest! I'd be happy to help.",
            "Let me get you some information on that.",
            "I can schedule a consultation for you this week.",
            "Our pricing varies based on your specific needs.",
            "The installation typically takes 2-3 days.",
            "Yes, we offer several financing options.",
            "I'll send you a detailed quote shortly.",
        ];

        $userMessages = [
            "Following up on this lead.",
            "Customer called with additional questions.",
            "Scheduled appointment for next Tuesday.",
            "Sent pricing information via email.",
            "Customer is ready to move forward.",
            "Need to reschedule the consultation.",
            "Project completed successfully.",
        ];

        $sender = $this->faker->randomElement($senders);
        $content = match ($sender) {
            MessageSender::LEAD => $this->faker->randomElement($leadMessages),
            MessageSender::AGENT => $this->faker->randomElement($agentMessages),
            MessageSender::USER => $this->faker->randomElement($userMessages),
        };

        return [
            'content' => $content,
            'sender' => $sender,
            'createdAt' => \DateTimeImmutable::createFromMutable($this->faker->dateTimeBetween('-30 days', 'now')),
        ];
    }

    public function withConversation(Conversation $conversation): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['conversation' => $conversation]);
    }

    public function withAuthor(User $author): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['author' => $author]);
    }

    public function withLead(Lead $lead): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['lead' => $lead]);
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }
}
