<?php
namespace Database\Factory;

use App\Database\Entity\ForwardPhoneNumber;
use App\Database\Entity\ForwardSequence;
use App\Database\Entity\PhoneNumber;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class ForwardPhoneNumberFactory extends AbstractFactory
{
    public function entity(): string
    {
        return ForwardPhoneNumber::class;
    }

    public function makeEntity(array $definition): ForwardPhoneNumber
    {
        $forwardPhoneNumber = new ForwardPhoneNumber();
        $forwardPhoneNumber->order = $definition['order'];
        $forwardPhoneNumber->waitTime = $definition['waitTime'];

        // Set relationships if provided
        if (isset($definition['forwardSequence'])) {
            $forwardPhoneNumber->forwardSequence = $definition['forwardSequence'];
        }
        if (isset($definition['phoneNumber'])) {
            $forwardPhoneNumber->phoneNumber = $definition['phoneNumber'];
        }

        return $forwardPhoneNumber;
    }

    public function definition(): array
    {
        return [
            'order' => $this->faker->numberBetween(1, 10),
            'waitTime' => $this->faker->numberBetween(5, 60), // Wait time in seconds
        ];
    }

    public function withForwardSequence(ForwardSequence $forwardSequence): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['forwardSequence' => $forwardSequence]);
    }

    public function withPhoneNumber(PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['phoneNumber' => $phoneNumber]);
    }
}
