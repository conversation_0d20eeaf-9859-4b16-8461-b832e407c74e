<?php
namespace Database\Factory;

use App\Database\Entity\AutoResponder;
use App\Database\Entity\Agent;
use App\Database\Enum\AutoResponderDelay;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class AutoResponderFactory extends AbstractFactory
{
    public function entity(): string
    {
        return AutoResponder::class;
    }

    public function makeEntity(array $definition): AutoResponder
    {
        $autoResponder = new AutoResponder();
        $autoResponder->enabled = $definition['enabled'];
        $autoResponder->toneSample = $definition['toneSample'] ?? null;
        $autoResponder->responseDelay = $definition['responseDelay'];

        // Set relationships if provided
        if (isset($definition['agent'])) {
            $autoResponder->agent = $definition['agent'];
        }

        return $autoResponder;
    }

    public function definition(): array
    {
        $delays = [
            AutoResponderDelay::NONE,
            AutoResponderDelay::VERY_SHORT,
            AutoResponderDelay::SHORT,
            AutoResponderDelay::MEDIUM,
            AutoResponderDelay::LONG,
            AutoResponderDelay::VERY_LONG,
            AutoResponderDelay::EXTREMELY_LONG,
            AutoResponderDelay::RANDOM,
        ];

        $tones = [
            'professional',
            'friendly',
            'casual',
            'formal',
            'enthusiastic',
            'helpful',
            'empathetic',
        ];

        return [
            'enabled' => $this->faker->boolean(70), // 70% chance of being enabled
            'toneSample' => $this->faker->randomElement($tones),
            'responseDelay' => $this->faker->randomElement($delays),
        ];
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }
}
