<?php
namespace Database\Factory;

use App\Database\Entity\Voicemail;
use App\Database\Entity\Call;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class VoicemailFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Voicemail::class;
    }

    public function makeEntity(array $definition): Voicemail
    {
        $voicemail = new Voicemail();
        $voicemail->transcription = $definition['transcription'];
        $voicemail->audioUrl = $definition['audioUrl'];
        $voicemail->duration = $definition['duration'];

        // Set relationships if provided
        if (isset($definition['call'])) {
            $voicemail->call = $definition['call'];
        }

        return $voicemail;
    }

    public function definition(): array
    {
        $transcriptions = [
            "Hi, this is <PERSON> calling about your services. I'm interested in getting a quote for a home renovation project. Please call me back at your earliest convenience. Thank you.",
            "Hello, I saw your ad online and I'm looking for someone to help with landscaping. Could you give me a call back to discuss pricing and availability? Thanks.",
            "Hi there, I'm calling to inquire about your maintenance services. I have a commercial property that needs regular upkeep. Please return my call when you get a chance.",
            "This is <PERSON> from ABC Company. We're looking for a contractor for our office renovation. Could you please call me back to discuss the project details?",
            "Hello, I'm interested in scheduling a consultation for kitchen remodeling. Please call me back at this number to set up an appointment. Thank you.",
            "Hi, I'm calling about emergency repair services. I have an urgent issue that needs attention. Please call me back as soon as possible.",
            "This is Mike calling about your installation services. I'd like to get more information and pricing. Please give me a call back when you have time.",
        ];

        $duration = $this->faker->numberBetween(15, 180); // 15 seconds to 3 minutes
        $audioUrl = 'https://s3.amazonaws.com/voicemails/' . $this->faker->uuid() . '.mp3';

        return [
            'transcription' => $this->faker->randomElement($transcriptions),
            'audioUrl' => $audioUrl,
            'duration' => $duration,
        ];
    }

    public function withCall(Call $call): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['call' => $call]);
    }
}
