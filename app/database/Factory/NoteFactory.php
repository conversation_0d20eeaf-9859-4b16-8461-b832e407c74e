<?php
namespace Database\Factory;

use App\Database\Entity\Appointment;
use App\Database\Entity\Conversation;
use App\Database\Entity\Lead;
use App\Database\Entity\Note;
use App\Database\Entity\User;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class NoteFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Note::class;
    }

    public function makeEntity(array $definition): Note
    {
        $note = new Note();
        $note->content = $definition['content'];

        if (isset($definition['lead'])) {
            $note->lead = $definition['lead'];
        }
        if (isset($definition['conversation'])) {
            $note->conversation = $definition['conversation'];
        }
        if (isset($definition['appointment'])) {
            $note->appointment = $definition['appointment'];
        }
        if (isset($definition['author'])) {
            $note->author = $definition['author'];
        }
        return $note;
    }

    public function withLead(Lead $lead): self {
        return $this->state(fn($faker, $def) => ['lead' => $lead]);
    }
    public function withConversation(Conversation $conversation): self {
        return $this->state(fn($faker, $def) => ['conversation' => $conversation]);
    }
    public function withAppointment(Appointment $appointment): self {
        return $this->state(fn($faker, $def) => ['appointment' => $appointment]);
    }

    public function definition(): array
    {
        $noteContents = [
            "Customer is very interested in our premium package. Follow up next week.",
            "Lead mentioned they have a tight budget. Consider offering financing options.",
            "Scheduled site visit for Tuesday at 2 PM. Customer will be available.",
            "Customer has specific requirements for eco-friendly materials.",
            "Competitor quoted $5,000 less. Need to justify our value proposition.",
            "Lead is ready to move forward but wants to wait until next month.",
            "Customer asked for references from similar projects in their area.",
            "Technical requirements are complex. May need specialist consultation.",
            "Customer is comparing multiple vendors. Emphasized our warranty.",
            "Lead showed strong interest but needs approval from business partner.",
            "Customer requested detailed timeline for project completion.",
            "Follow-up call scheduled for Friday to discuss contract terms.",
            "Customer mentioned potential for additional work in the future.",
            "Lead has seasonal constraints. Best to schedule for spring.",
            "Customer is price-sensitive but values quality workmanship.",
        ];

        return [
            'content' => $this->faker->randomElement($noteContents),
        ];
    }

    public function withAuthor(User $author): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['author' => $author]);
    }
}
