<?php
namespace Database\Factory;

use App\Database\Entity\Lead;
use App\Database\Entity\Person;
use App\Database\Entity\Organization;
use App\Database\Entity\Agent;
use App\Database\Entity\PhoneNumber;
use App\Database\Enum\LeadStatus;
use App\Database\Enum\LeadPriority;
use Doctrine\Common\Collections\ArrayCollection;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class LeadFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Lead::class;
    }

    public function makeEntity(array $definition): Lead
    {
        $lead = new Lead();
        $lead->service = $definition['service'] ?? null;
        $lead->status = $definition['status'];
        $lead->priority = $definition['priority'];
        $lead->estimatedValue = $definition['estimatedValue'] ?? null;
        $lead->actualValue = $definition['actualValue'] ?? null;
        $lead->estimatedCost = $definition['estimatedCost'] ?? null;
        $lead->lastContact = $definition['lastContact'] ?? null;
        $lead->score = $definition['score'] ?? null;

        // Set relationships if provided
        if (isset($definition['person'])) {
            $lead->person = $definition['person'];
        }
        if (isset($definition['organization'])) {
            $lead->organization = $definition['organization'];
        }
        if (isset($definition['sourceAgent'])) {
            $lead->sourceAgent = $definition['sourceAgent'];
        }
        if (isset($definition['phoneNumber'])) {
            $lead->phoneNumber = $definition['phoneNumber'];
        }

        return $lead;
    }

    public function definition(): array
    {
        $statuses = [
            LeadStatus::NEW,
            LeadStatus::CONTACTED,
            LeadStatus::QUALIFIED,
            LeadStatus::CONVERTED,
            LeadStatus::LOST,
        ];

        $priorities = [
            LeadPriority::HIGH,
            LeadPriority::MEDIUM,
            LeadPriority::LOW,
        ];

        $services = [
            'Consultation',
            'Installation',
            'Maintenance',
            'Repair',
            'Upgrade',
            'Training',
            'Support',
        ];

        $lastContact = $this->faker->optional(0.7)->dateTimeBetween('-30 days', 'now');
        if ($lastContact instanceof \DateTime) {
            $lastContact = \DateTimeImmutable::createFromMutable($lastContact);
        }

        return [
            'service' => $this->faker->optional(0.8)->randomElement($services),
            'status' => $this->faker->randomElement($statuses),
            'priority' => $this->faker->randomElement($priorities),
            'estimatedValue' => $this->faker->optional(0.7)->randomFloat(2, 100, 50000),
            'actualValue' => $this->faker->optional(0.3)->randomFloat(2, 100, 50000),
            'estimatedCost' => $this->faker->optional(0.6)->randomFloat(2, 50, 25000),
            'lastContact' => $lastContact,
            'score' => $this->faker->optional(0.9)->numberBetween(1, 100),
        ];
    }

    public function withPerson(Person $person): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['person' => $person]);
    }

    public function withOrganization(Organization $organization): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['organization' => $organization]);
    }

    public function withSourceAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['sourceAgent' => $agent]);
    }

    public function withAgents(array $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agents' => new ArrayCollection($agent)]);
    }

    public function withPhoneNumber(PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['phoneNumber' => $phoneNumber]);
    }

    public function withNotes(array $notes): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['notes' => new ArrayCollection($notes)]);
    }
}
