<?php
namespace Database\Factory;

use App\Database\Entity\Industry;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class IndustryFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Industry::class;
    }

    public function makeEntity(array $definition): Industry
    {
        $industry = new Industry();
        $industry->name = $definition['name'];
        $industry->description = $definition['description'] ?? null;

        return $industry;
    }

    public function withName(string $name): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['name' => $name]);
    }

    public function withDescription(string $description): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['description' => $description]);
    }

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->company(),
            'description' => $this->faker->sentence(8),
        ];
    }
}
