<?php
namespace Database\Factory;

use App\Database\Entity\Appointment;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Enum\AppointmentStatus;
use Doctrine\Common\Collections\ArrayCollection;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class AppointmentFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Appointment::class;
    }

    public function makeEntity(array $definition): Appointment
    {
        $appointment = new Appointment();
        $appointment->service = $definition['service'];
        $appointment->startTime = $definition['startTime'];
        $appointment->endTime = $definition['endTime'];
        $appointment->value = $definition['value'];
        $appointment->status = $definition['status'];

        // Set relationships if provided
        if (isset($definition['lead'])) {
            $appointment->lead = $definition['lead'];
        }
        if (isset($definition['agent'])) {
            $appointment->agent = $definition['agent'];
        }

        return $appointment;
    }

    public function definition(): array
    {
        $statuses = [
            AppointmentStatus::CONFIRMED,
            AppointmentStatus::PENDING,
            AppointmentStatus::RESCHEDULED,
            AppointmentStatus::CANCELLED,
        ];

        $services = [
            'Initial Consultation',
            'Site Survey',
            'Project Planning',
            'Installation',
            'Maintenance Check',
            'Follow-up Meeting',
            'Final Inspection',
            'Training Session',
        ];

        $startTime = $this->faker->dateTimeBetween('now', '+30 days');
        if ($startTime instanceof \DateTime) {
            $startTime = \DateTimeImmutable::createFromMutable($startTime);
        }
        $duration = $this->faker->numberBetween(1, 4); // 1-4 hours
        $endTime = (clone $startTime)->modify("+{$duration} hours");
        if ($endTime instanceof \DateTime) {
            $endTime = \DateTimeImmutable::createFromMutable($endTime);
        }

        return [
            'service' => $this->faker->randomElement($services),
            'startTime' => $startTime,
            'endTime' => $endTime,
            'value' => $this->faker->randomFloat(2, 100, 10000),
            'status' => $this->faker->randomElement($statuses),
        ];
    }

    public function withLead(Lead $lead): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['lead' => $lead]);
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }

    public function withNotes(array $notes): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['notes' => new ArrayCollection($notes)]);
    }
}
