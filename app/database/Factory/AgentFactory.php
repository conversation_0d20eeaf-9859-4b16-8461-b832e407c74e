<?php
namespace Database\Factory;

use App\Database\Entity\Agent;
use App\Database\Entity\PhoneNumber;
use App\Database\Entity\Organization;
use App\Database\Enum\AgentStatus;
use Faker\Generator;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class AgentFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Agent::class;
    }

    public function makeEntity(array $definition): Agent
    {
        $agent = new Agent();
        $agent->name = $definition['name'];
        $agent->status = $definition['status'];

        // Set relationships if provided
        if (isset($definition['phoneNumber'])) {
            $agent->phoneNumber = $definition['phoneNumber'];
        }
        if (isset($definition['organization'])) {
            $agent->organization = $definition['organization'];
        }

        return $agent;
    }

    public function definition(): array
    {
        $statuses = [
            AgentStatus::ACTIVE,
            AgentStatus::INACTIVE,
            AgentStatus::SCHEDULED,
        ];

        return [
            'name' => $this->faker->name(),
            'status' => $this->faker->randomElement($statuses),
        ];
    }

    public function withPhoneNumber(PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['phoneNumber' => $phoneNumber]);
    }

    public function withOrganization(Organization $organization): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['organization' => $organization]);
    }
}
