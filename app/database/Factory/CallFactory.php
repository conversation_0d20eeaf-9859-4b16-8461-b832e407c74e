<?php
namespace Database\Factory;

use App\Database\Entity\Call;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Entity\Conversation;
use App\Database\Entity\PhoneNumber;
use App\Database\Enum\CallStatus;
use Faker\Generator;
use <PERSON><PERSON>ral\DatabaseSeeder\Factory\AbstractFactory;

class CallFactory extends AbstractFactory
{
    public function entity(): string
    {
        return Call::class;
    }

    public function makeEntity(array $definition): Call
    {
        $call = new Call();
        $call->answered = $definition['answered'];
        $call->duration = $definition['duration'];
        $call->cost = $definition['cost'];
        $call->status = $definition['status'];
        $call->startTime = $definition['startTime'];
        $call->answerTime = $definition['answerTime'] ?? null;
        $call->endTime = $definition['endTime'];

        // Set relationships if provided
        if (isset($definition['lead'])) {
            $call->lead = $definition['lead'];
        }
        if (isset($definition['agent'])) {
            $call->agent = $definition['agent'];
        }
        if (isset($definition['conversation'])) {
            $call->conversation = $definition['conversation'];
        }
        if (isset($definition['answeredBy'])) {
            $call->answeredBy = $definition['answeredBy'];
        }

        return $call;
    }

    public function definition(): array
    {
        $statuses = [
            CallStatus::RINGING,
            CallStatus::ANSWERED,
            CallStatus::IN_PROGRESS,
            CallStatus::BUSY,
            CallStatus::NO_ANSWER,
            CallStatus::FAILED,
            CallStatus::CANCELLED,
        ];

        $status = $this->faker->randomElement($statuses);
        $answered = in_array($status, [CallStatus::ANSWERED, CallStatus::IN_PROGRESS]);

        $startTime = $this->faker->dateTimeBetween('-30 days', 'now');
        if ($startTime instanceof \DateTime) {
            $startTime = \DateTimeImmutable::createFromMutable($startTime);
        }

        $duration = $answered ? $this->faker->numberBetween(30, 1800) : 0; // 30 seconds to 30 minutes

        $endTime = (clone $startTime)->modify("+{$duration} seconds");
        if ($endTime instanceof \DateTime) {
            $endTime = \DateTimeImmutable::createFromMutable($endTime);
        }

        $answerTime = $answered ? (clone $startTime)->modify('+' . $this->faker->numberBetween(1, 10) . ' seconds') : null;
        if ($answerTime instanceof \DateTime) {
            $answerTime = \DateTimeImmutable::createFromMutable($answerTime);
        }

        return [
            'answered' => $answered,
            'duration' => $duration,
            'cost' => $this->faker->randomFloat(4, 0.01, 5.00), // Cost in dollars
            'status' => $status,
            'startTime' => $startTime,
            'answerTime' => $answerTime,
            'endTime' => $endTime,
        ];
    }

    public function withLead(Lead $lead): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['lead' => $lead]);
    }

    public function withAgent(Agent $agent): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['agent' => $agent]);
    }

    public function withConversation(Conversation $conversation): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['conversation' => $conversation]);
    }

    public function withAnsweredBy(PhoneNumber $phoneNumber): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['answeredBy' => $phoneNumber]);
    }
}
