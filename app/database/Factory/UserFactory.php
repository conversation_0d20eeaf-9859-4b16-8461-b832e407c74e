<?php
namespace Database\Factory;

use App\Database\Entity\User;
use Faker\Generator;
use Ramsey\Uuid\Uuid;
use Spiral\DatabaseSeeder\Factory\AbstractFactory;

class UserFactory extends AbstractFactory
{
    public function entity(): string
    {
        return User::class;
    }

    public function makeEntity(array $definition): User
    {
        $user = new User();
        $user->username = $definition['username'];
        $user->password = $definition['password'] ?? null;
        $user->verified = (bool)($definition['verified'] ?? false);

        // Set relationships if provided
        if (isset($definition['person'])) {
            $user->person = $definition['person'];
        }
        if (isset($definition['organization'])) {
            $user->organization = $definition['organization'];
        }

        return $user;
    }

    public function definition(): array
    {
        return [
            'username' => $this->faker->unique()->email(),
            // For demo data we hash a default password; replace with your hashing strategy if needed.
            'password' => password_hash('password', PASSWORD_BCRYPT),
            'verified' => $this->faker->boolean(20),
        ];
    }

    public function withPerson(\App\Database\Entity\Person $person): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['person' => $person]);
    }

    public function withOrganization(\App\Database\Entity\Organization $organization): self
    {
        return $this->state(fn(Generator $faker, array $definition) => ['organization' => $organization]);
    }
}
