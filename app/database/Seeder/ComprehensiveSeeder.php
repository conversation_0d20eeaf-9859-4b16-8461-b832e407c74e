<?php

namespace Database\Seeder;

use App\Database\Enum\MessageSender;
use Database\Factory\AddressFactory;
use Database\Factory\AgentFactory;
use Database\Factory\AppointmentFactory;
use Database\Factory\AutoResponderFactory;
use Database\Factory\CallFactory;
use Database\Factory\ConversationFactory;
use Database\Factory\ForwardPhoneNumberFactory;
use Database\Factory\ForwardSequenceFactory;
use Database\Factory\IndustryFactory;
use Database\Factory\LeadFactory;
use Database\Factory\MessageFactory;
use Database\Factory\NoteFactory;
use Database\Factory\OrganizationFactory;
use Database\Factory\PersonFactory;
use Database\Factory\PhoneNumberFactory;
use Database\Factory\UserFactory;
use Database\Factory\VoicemailFactory;
use Doctrine\Common\Collections\ArrayCollection;
use Faker\Factory as FakerFactory;
use Faker\Generator;
use Spiral\DatabaseSeeder\Attribute\Seeder;
use Spiral\DatabaseSeeder\Seeder\AbstractSeeder;

#[Seeder]
class ComprehensiveSeeder extends AbstractSeeder
{
    private Generator $faker;

    public function __construct(?int $priority = null)
    {
        parent::__construct($priority);

        $this->faker = FakerFactory::create();
    }

    public function run(): \Generator
    {
        // Step 1: Seed Industries using existing IndustrySeeder
        $industries = [];
        foreach ($this->listIndustries() as $industryData) {
            $industries[] = IndustryFactory::new()->state(fn(Generator $faker, array $definition) => $industryData)->createOne();
        }
        yield from $industries;

        // Step 2: Create Addresses (20 addresses for various uses)
        $addresses = AddressFactory::new()->times(20)->make();
        yield from $addresses;

        // Step 3: Create Phone Numbers (30 phone numbers for various uses)
        $phoneNumbers = PhoneNumberFactory::new()->times(30)->make();
        yield from $phoneNumbers;

        // Step 4: Create Organizations (3 organizations)
        $organizations = [];
        for ($i = 0; $i < 3; $i++) {
            $organizations[] = OrganizationFactory::new()
                ->withIndustry($this->faker->randomElement($industries))
                ->withAddress($this->faker->randomElement($addresses))
                ->withPhoneNumber($this->faker->randomElement($phoneNumbers))
                ->createOne();
        }
        yield from $organizations;

        // Step 5: Create People (20 people - some will be users, some leads)
        $people = [];
        for ($i = 0; $i < 20; $i++) {
            $people[] = PersonFactory::new()
                ->withPhoneNumber($this->faker->randomElement($phoneNumbers))
                ->withAddress($this->faker->optional(0.7)->randomElement($addresses))
                ->createOne();
        }
        yield from $people;

        // Step 6: Create Users (4 users)
        $users = [];
        for ($i = 0; $i < 4; $i++) {
            $users[] = UserFactory::new()
                ->withPerson($people[$i])
                ->withOrganization($this->faker->randomElement($organizations))
                ->state(fn(Generator $faker, array $definition) => [
                    'username'=>$people[$i]->email,
                ])
                ->createOne();
        }
        yield from $users;

        // Step 7: Create Agents (9 agents, 3 per organization)
        $agents = [];
        foreach ($organizations as $orgIndex => $organization) {
            for ($i = 0; $i < 3; $i++) {
                $agents[] = AgentFactory::new()
                    ->withPhoneNumber($this->faker->randomElement($phoneNumbers))
                    ->withOrganization($organization)
                    ->createOne();
            }
        }
        yield from $agents;

        // Step 8: Create AutoResponders for agents (70% of agents have auto responders)
        foreach ($agents as $agent) {
            if ($this->faker->boolean(70)) {
                yield AutoResponderFactory::new()->withAgent($agent)
                    ->createOne();
            }
        }

        // Step 9: Create Forward Sequences for agents (50% of agents have forward sequences)
        $forwardSequences = [];
        foreach ($agents as $agent) {
            if ($this->faker->boolean(50)) {
                $forwardSequence = ForwardSequenceFactory::new()->withAgent($agent)->createOne();
                $forwardSequences[] = $forwardSequence;

                yield $forwardSequence;

                // Create 2-4 forward phone numbers per sequence
                $forwardCount = $this->faker->numberBetween(2, 4);
                for ($i = 1; $i <= $forwardCount; $i++) {
                    yield ForwardPhoneNumberFactory::new()
                        ->withForwardSequence($forwardSequence)
                        ->withPhoneNumber($this->faker->randomElement($phoneNumbers))
                        ->state(fn(Generator $faker, array $definition) => ['order' => $i])
                        ->createOne();
                }
            }
        }

        $pickAuthor = fn() => $this->faker->randomElement([$users[0],$users[1],$users[2],$users[3]]);

        // Step 10: Create Leads (15 leads)
        $leadNoteSamples = [
            "Initial intake complete; awaiting photos of the job site.",
            "Lead asked about financing; send options after estimate.",
            "Budget is tight—prioritize must-haves in first proposal.",
            "Prefers weekday mornings; avoid Fridays if possible.",
            "Referred by a neighbor; emphasize warranty & reliability.",
        ];
        $leads = [];
        for ($i = 0; $i < 15; $i++) {
            $personIndex = $i + 4; // Skip the first 4 people who are users
            $notes = [];
            for ($ni = 0; $ni < $this->faker->numberBetween(1, 3); $ni++) {
                $notes[] = NoteFactory::new()
                    ->withAuthor($pickAuthor())
                    ->state(fn(Generator $faker, array $def) => [
                        'content' => $faker->randomElement($leadNoteSamples),
                    ])
                    ->createOne();
            }

            yield from $notes;
            $leads[] = LeadFactory::new()
                ->withPerson($people[$personIndex])
                ->withOrganization($this->faker->randomElement($organizations))
                ->withSourceAgent($this->faker->randomElement($agents))
                ->withAgents($this->faker->randomElements($agents))
                ->withPhoneNumber($this->faker->randomElement($phoneNumbers))
                ->withNotes($notes)
                ->createOne();
        }
        yield from $leads;

        // Step 11: Create Conversations (12 conversations - 80% of leads have conversations)

        $conversationNoteSamples = [
            "Tone was positive; customer liked our timeline & clarity.",
            "Clarified scope creep risks; customer agreed to phased plan.",
            "Follow-up questions answered; next step is scheduling.",
            "Escalated to senior tech for unusual site constraints.",
            "Customer wants a written summary; send after call.",
        ];
        $conversations = [];
        $conversationLeads = $leads;
        foreach ($conversationLeads as $lead) {
            $notes = [];
            for ($ni = 0; $ni < $this->faker->numberBetween(1, 3); $ni++) {
                $notes[] = NoteFactory::new()
                    ->withAuthor($pickAuthor())
                    ->state(fn(Generator $faker, array $def) => [
                        'content' => $faker->randomElement($conversationNoteSamples),
                    ])
                    ->createOne();
            }

            yield from $notes;
            $conversations[] = ConversationFactory::new()
                ->withLead($lead)
                ->withAgent($this->faker->randomElement($agents))
                ->withNotes($notes)
                ->createOne();
        }
        yield from $conversations;

        // Step 12: Create Calls (25 calls - realistic call volume)
        $calls = [];
        for ($i = 0; $i < 25; $i++) {
            $call = CallFactory::new()
                ->withLead($this->faker->randomElement($leads))
                ->withAgent($this->faker->randomElement($agents))
                ->withConversation($this->faker->randomElement($conversations))
                ->createOne();
            $calls[] = $call;
        }
        yield from $calls;

        // Step 13: Create Voicemails (8 voicemails - about 30% of calls have voicemails)
        $unansweredCalls = array_filter($calls, fn($call) => !$call->answered);
        $voicemailCalls = $this->faker->randomElements($unansweredCalls, min(8, count($unansweredCalls)));
        foreach ($voicemailCalls as $call) {
            yield VoicemailFactory::new()->withCall($call)->createOne();
        }

        // Step 14: Create Messages (60 messages - active conversation volume)
        for ($i = 0; $i < 60; $i++) {
            $conversation = $this->faker->randomElement($conversations);
            $sender = $this->faker->randomElement(['lead', 'agent', 'user']);

            $messageFactory = MessageFactory::new()->state(fn(Generator $faker, array $definition) => ['sender' => MessageSender::from($sender)])
                ->withConversation($conversation);
            // Set appropriate relationships based on sender
            switch ($sender) {
                case 'lead':
                    $messageFactory = $messageFactory->withLead($conversation->lead);
                    break;
                case 'agent':
                    $messageFactory = $messageFactory->withAgent($conversation->agent);
                    break;
                case 'user':
                    $messageFactory = $messageFactory->withAuthor($this->faker->randomElement($users));
                    break;
            }

            yield $messageFactory->createOne();
        }

        // Step 15: Create Appointments (10 appointments - scheduled meetings)
        $appointmentNoteSamples = [
            "Arrived on time; site was accessible and ready.",
            "Found minor wiring issues—add line item to quote.",
            "Customer asked for material samples at next visit.",
            "Measured twice; final measurements captured in file.",
            "Rescheduled due to weather; confirmed new time.",
        ];

        $appointments = [];
        for ($i = 0; $i < 10; $i++) {
            $notes = [];
            for($ni = 0; $ni < $this->faker->numberBetween(1, 3); $ni++) {
                $notes[] = NoteFactory::new()
                    ->withAuthor($pickAuthor())
                    ->state(fn(Generator $faker, array $def) => [
                        'content' => $faker->randomElement($appointmentNoteSamples),
                    ])
                    ->createOne();

            }
            yield from $notes;
            $appointments[] = AppointmentFactory::new()
                ->withLead($this->faker->randomElement($leads))
                ->withAgent($this->faker->randomElement($agents))
                ->withNotes($notes)
                ->createOne();
        }
        yield from $appointments;

        // Step 16: Create Notes (35 notes - documentation and follow-ups)

        foreach ($leads as $lead) {
            $count = $this->faker->numberBetween(1, 3);
            for ($i = 0; $i < $count; $i++) {
                $note = NoteFactory::new()
                    ->withAuthor($pickAuthor())
                    ->state(fn(Generator $faker, array $def) => [
                        'content' => $faker->randomElement($leadNoteSamples),
                    ])
                    ->createOne();
                $lead->notes->add($note);
                yield $note;
            }
        }

        foreach ($conversations as $conversation) {
            $count = $this->faker->numberBetween(1, 2);
            for ($i = 0; $i < $count; $i++) {
                $note = NoteFactory::new()
                    ->withAuthor($pickAuthor())
                    ->state(fn(Generator $faker, array $def) => [
                        'content' => $faker->randomElement($conversationNoteSamples),
                    ])
                    ->createOne();
                $conversation->notes->add($note);
                yield $note;
            }
        }

        return "Comprehensive seeding completed successfully!\n" .
            "Created:\n" .
            "- 20 Industries (from IndustrySeeder)\n" .
            "- 20 Addresses\n" .
            "- 30 Phone Numbers\n" .
            "- 20 People\n" .
            "- 4 Users\n" .
            "- 3 Organizations\n" .
            "- 9 Agents\n" .
            "- 15 Leads\n" .
            "- 12 Conversations\n" .
            "- 25 Calls\n" .
            "- 8 Voicemails\n" .
            "- 60 Messages\n" .
            "- 35 Notes\n" .
            "- 10 Appointments\n" .
            "- Auto Responders and Forward Sequences for applicable agents";
    }

    public function listIndustries(): array
    {
        return [
            [
                'name' => 'Plumbing',
                'description' => 'Plumbing services including repairs, installations, and maintenance',
            ],
            [
                'name' => 'Electrical',
                'description' => 'Electrical services including repairs, installations, and maintenance',
            ],
            [
                'name' => 'HVAC',
                'description' => 'Heating, ventilation, and air conditioning services',
            ],
            [
                'name' => 'Landscaping',
                'description' => 'Landscaping and lawn care services',
            ],
            [
                'name' => 'Cleaning',
                'description' => 'Residential and commercial cleaning services',
            ],
            [
                'name' => 'Roofing',
                'description' => 'Roof installation, repair, and maintenance services',
            ],
            [
                'name' => 'Carpentry',
                'description' => 'Carpentry and woodworking services',
            ],
            [
                'name' => 'Painting',
                'description' => 'Interior and exterior painting services',
            ],
            [
                'name' => 'Flooring',
                'description' => 'Flooring installation and repair services',
            ],
            [
                'name' => 'Pest Control',
                'description' => 'Pest control and extermination services',
            ],
            [
                'name' => 'Locksmith',
                'description' => 'Locksmith and security services',
            ],
            [
                'name' => 'Moving',
                'description' => 'Residential and commercial moving services',
            ],
            [
                'name' => 'Appliance Repair',
                'description' => 'Repair and maintenance of household appliances',
            ],
            [
                'name' => 'Window Cleaning',
                'description' => 'Residential and commercial window cleaning services',
            ],
            [
                'name' => 'Garage Door',
                'description' => 'Garage door installation, repair, and maintenance',
            ],
            [
                'name' => 'Tree Service',
                'description' => 'Tree trimming, removal, and maintenance services',
            ],
            [
                'name' => 'Handyman',
                'description' => 'General home repair and maintenance services',
            ],
            [
                'name' => 'Concrete',
                'description' => 'Concrete installation, repair, and maintenance',
            ],
            [
                'name' => 'Fencing',
                'description' => 'Fence installation, repair, and maintenance',
            ],
            [
                'name' => 'Drywall',
                'description' => 'Drywall installation, repair, and finishing',
            ],
        ];
    }
}
