<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefault48b4c63e405006ab22c760de507f888a extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('users')
        ->alterColumn('organization_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->update();
    }

    public function down(): void
    {
        $this->table('users')
        ->alterColumn('organization_id', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->update();
    }
}
