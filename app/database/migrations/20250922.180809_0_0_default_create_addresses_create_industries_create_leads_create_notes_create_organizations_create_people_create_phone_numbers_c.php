<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefault7d6f7c1edae5035af129eec5e68750f6 extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('addresses')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('street1', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('street2', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('street3', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('street4', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('city', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('state', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('zip', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('industries')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('name', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('description', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('phone_numbers')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('number', 'integer', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('type', 'enum', [
            'nullable' => false,
            'defaultValue' => 'virtual',
            'values' => ['landline', 'virtual', 'mobile', 'fax'],
        ])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'active',
            'values' => ['active', 'inactive', 'pending'],
        ])
        ->addColumn('monthly_rate', 'float', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('purchase_date', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('people')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('first_name', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('last_name', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('email', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('phone_number_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addColumn('author_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['phone_number_id'], ['name' => 'people_index_phone_number_id_68d19089ab0f9', 'unique' => false])
        ->addIndex(['author_id'], ['name' => 'people_index_author_id_68d19089ab186', 'unique' => false])
        ->addForeignKey(['phone_number_id'], 'phone_numbers', ['id'], [
            'name' => 'people_phone_number_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['author_id'], 'addresses', ['id'], [
            'name' => 'people_author_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('organizations')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('name', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('industry_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('address_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('phone_number_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['industry_id'], ['name' => 'organizations_index_industry_id_68d19089aad00', 'unique' => false])
        ->addIndex(['address_id'], ['name' => 'organizations_index_address_id_68d19089aad84', 'unique' => false])
        ->addIndex(['phone_number_id'], ['name' => 'organizations_index_phone_number_id_68d19089aae13', 'unique' => false])
        ->addForeignKey(['industry_id'], 'industries', ['id'], [
            'name' => 'organizations_industry_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['address_id'], 'addresses', ['id'], [
            'name' => 'organizations_address_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['phone_number_id'], 'phone_numbers', ['id'], [
            'name' => 'organizations_phone_number_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('agents')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('name', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'active',
            'values' => ['active', 'inactive', 'scheduled'],
        ])
        ->addColumn('organization_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('phone_number_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['organization_id'], ['name' => 'agents_index_organization_id_68d19089ab069', 'unique' => false])
        ->addIndex(['phone_number_id'], ['name' => 'agents_index_phone_number_id_68d19089ab39e', 'unique' => false])
        ->addForeignKey(['organization_id'], 'organizations', ['id'], [
            'name' => 'agents_organization_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['phone_number_id'], 'phone_numbers', ['id'], [
            'name' => 'agents_phone_number_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('leads')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('service', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'new',
            'values' => ['new', 'contacted', 'qualified', 'converted', 'lost'],
        ])
        ->addColumn('priority', 'enum', ['nullable' => false, 'defaultValue' => 'low', 'values' => ['high', 'medium', 'low']])
        ->addColumn('estimated_value', 'float', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('actual_value', 'float', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('estimated_cost', 'float', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('last_contact', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('score', 'integer', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('person_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('organization_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('phone_number_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['person_id'], ['name' => 'leads_index_person_id_68d19089aa585', 'unique' => false])
        ->addIndex(['organization_id'], ['name' => 'leads_index_organization_id_68d19089aa727', 'unique' => false])
        ->addIndex(['phone_number_id'], ['name' => 'leads_index_phone_number_id_68d19089aa7d1', 'unique' => false])
        ->addIndex(['agent_id'], ['name' => 'leads_index_agent_id_68d19089aabd0', 'unique' => false])
        ->addForeignKey(['person_id'], 'people', ['id'], [
            'name' => 'leads_person_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['organization_id'], 'organizations', ['id'], [
            'name' => 'leads_organization_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['phone_number_id'], 'phone_numbers', ['id'], [
            'name' => 'leads_phone_number_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'leads_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('users')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('username', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('password', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255])
        ->addColumn('verified', 'boolean', ['nullable' => false, 'defaultValue' => false, 'size' => 1])
        ->addColumn('organization_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addColumn('person_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['organization_id'], ['name' => 'users_index_organization_id_68d19089aaee7', 'unique' => false])
        ->addIndex(['person_id'], ['name' => 'users_index_person_id_68d19089ab259', 'unique' => false])
        ->addForeignKey(['organization_id'], 'organizations', ['id'], [
            'name' => 'users_organization_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['person_id'], 'people', ['id'], [
            'name' => 'users_person_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('notes')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('content', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('author_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['author_id'], ['name' => 'notes_index_author_id_68d19089aac7a', 'unique' => false])
        ->addForeignKey(['author_id'], 'users', ['id'], [
            'name' => 'notes_author_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('forward_sequences')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['agent_id'], ['name' => 'forward_sequences_index_agent_id_68d19089aba31', 'unique' => false])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'forward_sequences_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('auto_responders')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('enabled', 'boolean', ['nullable' => false, 'defaultValue' => false, 'size' => 1])
        ->addColumn('tone_sample', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('response_delay', 'enum', [
            'nullable' => false,
            'defaultValue' => '0',
            'values' => ['0', '1-30', '30-60', '60-300', '300-600', '600-1800', '1800-3600', 'random'],
        ])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['agent_id'], ['name' => 'auto_responders_index_agent_id_68d19089abafc', 'unique' => false])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'auto_responders_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('conversations')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'active',
            'values' => ['active', 'pending', 'resolved', 'archived'],
        ])
        ->addColumn('priority', 'enum', ['nullable' => false, 'defaultValue' => 'low', 'values' => ['high', 'medium', 'low']])
        ->addColumn('is_agent_active', 'boolean', ['nullable' => false, 'defaultValue' => true, 'size' => 1])
        ->addColumn('lead_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['lead_id'], ['name' => 'conversations_index_lead_id_68d19089aaa6b', 'unique' => false])
        ->addIndex(['agent_id'], ['name' => 'conversations_index_agent_id_68d19089ab7be', 'unique' => false])
        ->addForeignKey(['lead_id'], 'leads', ['id'], [
            'name' => 'conversations_lead_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'conversations_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('appointments')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('service', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('start_time', 'datetime', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('end_time', 'datetime', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('value', 'float', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'pending',
            'values' => ['confirmed', 'pending', 'rescheduled', 'cancelled'],
        ])
        ->addColumn('lead_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('conversation_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['lead_id'], ['name' => 'appointments_index_lead_id_68d19089aab39', 'unique' => false])
        ->addIndex(['agent_id'], ['name' => 'appointments_index_agent_id_68d19089ab8a3', 'unique' => false])
        ->addIndex(['conversation_id'], ['name' => 'appointments_index_conversation_id_68d19089ac241', 'unique' => false])
        ->addForeignKey(['lead_id'], 'leads', ['id'], [
            'name' => 'appointments_lead_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'appointments_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['conversation_id'], 'conversations', ['id'], [
            'name' => 'appointments_conversation_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('calls')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('answered', 'boolean', ['nullable' => false, 'defaultValue' => false, 'size' => 1])
        ->addColumn('duration', 'integer', ['nullable' => false, 'defaultValue' => 0])
        ->addColumn('cost', 'float', ['nullable' => false, 'defaultValue' => 0.0])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'ringing',
            'values' => ['ringing', 'answered', 'in_progress', 'busy', 'no_answer', 'failed', 'cancelled'],
        ])
        ->addColumn('start_time', 'datetime', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('answer_time', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('end_time', 'datetime', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('conversation_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('lead_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('agent_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('answered_by_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['conversation_id'], ['name' => 'calls_index_conversation_id_68d19089abe41', 'unique' => false])
        ->addIndex(['lead_id'], ['name' => 'calls_index_lead_id_68d19089ac483', 'unique' => false])
        ->addIndex(['agent_id'], ['name' => 'calls_index_agent_id_68d19089ac526', 'unique' => false])
        ->addIndex(['answered_by_id'], ['name' => 'calls_index_answered_by_id_68d19089ac646', 'unique' => false])
        ->addForeignKey(['conversation_id'], 'conversations', ['id'], [
            'name' => 'calls_conversation_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['lead_id'], 'leads', ['id'], [
            'name' => 'calls_lead_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'calls_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['answered_by_id'], 'phone_numbers', ['id'], [
            'name' => 'calls_answered_by_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('voicemails')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('transcription', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('audio_url', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('duration', 'integer', ['nullable' => false, 'defaultValue' => 0])
        ->addColumn('call_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['call_id'], ['name' => 'voicemails_index_call_id_68d19089ac72b', 'unique' => false])
        ->addForeignKey(['call_id'], 'calls', ['id'], [
            'name' => 'voicemails_call_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('messages')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('content', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255])
        ->addColumn('sender', 'enum', ['nullable' => false, 'defaultValue' => 'lead', 'values' => ['lead', 'agent', 'user']])
        ->addColumn('conversation_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('author_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addColumn('lead_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addColumn('agent_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['conversation_id'], ['name' => 'messages_index_conversation_id_68d19089abf15', 'unique' => false])
        ->addIndex(['author_id'], ['name' => 'messages_index_author_id_68d19089ac8bf', 'unique' => false])
        ->addIndex(['lead_id'], ['name' => 'messages_index_lead_id_68d19089ac956', 'unique' => false])
        ->addIndex(['agent_id'], ['name' => 'messages_index_agent_id_68d19089ac9f8', 'unique' => false])
        ->addForeignKey(['conversation_id'], 'conversations', ['id'], [
            'name' => 'messages_conversation_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['author_id'], 'users', ['id'], [
            'name' => 'messages_author_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['lead_id'], 'leads', ['id'], [
            'name' => 'messages_lead_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['agent_id'], 'agents', ['id'], [
            'name' => 'messages_agent_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('forward_phone_numbers')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('order', 'integer', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('wait_time', 'integer', ['nullable' => false, 'defaultValue' => null])
        ->addColumn('forward_sequence_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('phone_number_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['forward_sequence_id'], [
            'name' => 'forward_phone_numbers_index_forward_sequence_id_68d19089abc0f',
            'unique' => false,
        ])
        ->addIndex(['phone_number_id'], [
            'name' => 'forward_phone_numbers_index_phone_number_id_68d19089acb12',
            'unique' => false,
        ])
        ->addForeignKey(['forward_sequence_id'], 'forward_sequences', ['id'], [
            'name' => 'forward_phone_numbers_forward_sequence_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['phone_number_id'], 'phone_numbers', ['id'], [
            'name' => 'forward_phone_numbers_phone_number_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('lead_notes')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('lead_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('note_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['lead_id', 'note_id'], ['name' => 'lead_notes_index_lead_id_note_id_68d19089aa942', 'unique' => true])
        ->addIndex(['lead_id'], ['name' => 'lead_notes_index_lead_id_68d19089aa982', 'unique' => false])
        ->addIndex(['note_id'], ['name' => 'lead_notes_index_note_id_68d19089aa9c4', 'unique' => false])
        ->addForeignKey(['lead_id'], 'leads', ['id'], [
            'name' => 'lead_notes_lead_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['note_id'], 'notes', ['id'], [
            'name' => 'lead_notes_note_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('conversation_notes')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('conversation_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('note_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['conversation_id', 'note_id'], [
            'name' => 'conversation_notes_index_conversation_id_note_id_68d19089ac062',
            'unique' => true,
        ])
        ->addIndex(['conversation_id'], ['name' => 'conversation_notes_index_conversation_id_68d19089ac09c', 'unique' => false])
        ->addIndex(['note_id'], ['name' => 'conversation_notes_index_note_id_68d19089ac0dc', 'unique' => false])
        ->addForeignKey(['conversation_id'], 'conversations', ['id'], [
            'name' => 'conversation_notes_conversation_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['note_id'], 'notes', ['id'], [
            'name' => 'conversation_notes_note_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
        $this->table('appointment_notes')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('created_at', 'datetime', ['nullable' => false, 'defaultValue' => 'CURRENT_TIMESTAMP'])
        ->addColumn('updated_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('deleted_at', 'datetime', ['nullable' => true, 'defaultValue' => null])
        ->addColumn('appointment_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addColumn('note_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['appointment_id', 'note_id'], [
            'name' => 'appointment_notes_index_appointment_id_note_id_68d19089ac394',
            'unique' => true,
        ])
        ->addIndex(['appointment_id'], ['name' => 'appointment_notes_index_appointment_id_68d19089ac3d5', 'unique' => false])
        ->addIndex(['note_id'], ['name' => 'appointment_notes_index_note_id_68d19089ac416', 'unique' => false])
        ->addForeignKey(['appointment_id'], 'appointments', ['id'], [
            'name' => 'appointment_notes_appointment_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['note_id'], 'notes', ['id'], [
            'name' => 'appointment_notes_note_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
    }

    public function down(): void
    {
        $this->table('appointment_notes')->drop();
        $this->table('conversation_notes')->drop();
        $this->table('lead_notes')->drop();
        $this->table('forward_phone_numbers')->drop();
        $this->table('messages')->drop();
        $this->table('voicemails')->drop();
        $this->table('calls')->drop();
        $this->table('appointments')->drop();
        $this->table('conversations')->drop();
        $this->table('auto_responders')->drop();
        $this->table('forward_sequences')->drop();
        $this->table('notes')->drop();
        $this->table('users')->drop();
        $this->table('leads')->drop();
        $this->table('agents')->drop();
        $this->table('organizations')->drop();
        $this->table('people')->drop();
        $this->table('phone_numbers')->drop();
        $this->table('industries')->drop();
        $this->table('addresses')->drop();
    }
}
