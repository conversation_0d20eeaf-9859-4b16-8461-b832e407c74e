<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefaultE1068bd8b1418a3d3e2738cec21fa859 extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('appointments')
        ->addColumn('conversation_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'size' => 36])
        ->addIndex(['conversation_id'], ['name' => 'appointments_index_conversation_id_68c786a86dbf4', 'unique' => false])
        ->addForeignKey(['conversation_id'], 'conversations', ['id'], [
            'name' => 'appointments_conversation_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->update();
    }

    public function down(): void
    {
        $this->table('appointments')
        ->dropForeignKey(['conversation_id'])
        ->dropIndex(['conversation_id'])
        ->dropColumn('conversation_id')
        ->update();
    }
}
