<?php

namespace App\Middleware;

use <PERSON><PERSON>holm\Psr7\Response;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class CorsMiddleware implements MiddlewareInterface
{

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $origin = $request->getHeaderLine('Origin');
        if ($request->getMethod() === 'OPTIONS') {
            $response = new Response(200);
            $response = $response->withHeader('Access-Control-Allow-Origin', '*');
            $response = $response->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            $response = $response->withHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            $response = $response->withHeader('Access-Control-Allow-Credentials', 'true');
            return $response;
        }

        $response = $handler->handle($request);
//        if ($origin) {
            $response = $response->withHeader('Access-Control-Allow-Origin', '*');
            $response = $response->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            $response = $response->withHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            $response = $response->withHeader('Access-Control-Allow-Credentials', 'true');
//        }

        return $response;
    }
}
