<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Agent;
use App\Database\Enum\AgentStatus;
use App\Endpoint\Web\View\AgentView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Filter\InArray;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\AscSorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\EnumValue;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'agentGridSchema')]
class AgentGridSchema extends GridSchema
{
    public function __construct(
        private readonly AgentView $agentView
    )
    {
        $this->addFilter('name', new Equals('name', new StringValue()));
        $this->addFilter('status', new InArray('status', new EnumValue(new StringValue(), AgentStatus::class)));
        $this->addFilter('organization', new InArray('organization.id', new StringValue()));

        $this->addSorter('default',new SorterSet(
            new AscSorter('status'),
            new AscSorter('name'),
        ));

        $this->setPaginator(new PagePaginator(12, [24, 48]));
    }

    public function __invoke(Agent $agent)
    {
        return $this->agentView->map($agent);
    }
}
