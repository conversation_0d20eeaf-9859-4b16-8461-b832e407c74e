<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Conversation;
use App\Endpoint\Web\View\ConversationView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Sorter\Sorter;
use Spiral\DataGrid\Specification\Value\UuidValue;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'conversationGridSchema')]
class ConversationGridSchema extends GridSchema
{
    public function __construct(
        private readonly ConversationView $conversationView
    )
    {
        $this->addFilter('lead', new Equals('lead.id', new UuidValue()));
        $this->addFilter('agent', new Equals('agent.id', new UuidValue()));

        $this->addSorter('createdAt',new Sorter('created_at'));
        $this->addSorter('lastMessageTime',new Sorter('messages.created_at'));


    }

    public function __invoke(Conversation $conversation)
    {
        return $this->conversationView->map($conversation);
    }
}
