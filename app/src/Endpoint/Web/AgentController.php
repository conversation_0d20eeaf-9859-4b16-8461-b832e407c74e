<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Agent;
use App\Database\Entity\Conversation;
use App\Database\Entity\Lead;
use App\Endpoint\Web\Filter\AgentGridSchema;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use RoadRunner\Logger\Logger;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class AgentController
{
    use PrototypeTrait;

    #[Route(route: '/agent', name: 'agent.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: AgentGridSchema::class)]
    public function list(): Select
    {
        return $this->agents->select();
    }

    #[Route(route: '/agent/<agent>', name: 'agent.get', methods: 'GET', group: 'v1')]
    public function get(Agent $agent): ResponseInterface
    {
        return $this->agentView->json($agent);
    }

    #[Route(route: '/agent/<agent>', name: 'agent.update', methods: 'PATCH', group: 'v1')]
    public function update(Agent $agent): ResponseInterface
    {
        $this->logger->info('Updating agent', [
            'agent' => $agent->id,
        ]);

        $agent->name = $this->request->getParsedBody()['name'] ?? $agent->name;
        $agent->status = $this->request->getParsedBody()['status'] ?? $agent->status;
        $this->entityManager->persist($agent);
        $this->entityManager->run();

        // @todo
        return $this->response->json([
            'status'=>200,
            'data'=>[
                'id'=>$agent->id,
                'name'=>$agent->name,
                'phone'=>$agent->phoneNumber->number,
                'status'=>$agent->status->value,
                'forwardNumber'=>$agent->forwardNumber,
                'forwardSequence'=>$agent->forwardSequence,
                'autoResponder'=>[
                    'enabled'=>$agent->autoResponder->enabled,
                    'toneSample'=>$agent->autoResponder->toneSample,
                    'responseDelay'=>$agent->autoResponder->responseDelay->value,
                ],
            ],
        ]);
    }

    #[Route(route: '/agent/<agent>/configure', name: 'agent.configure', methods: 'GET', group: 'v1')]
    public function configure(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>[
                'id'=>$agent->id,
                'name'=>$agent->name,
                'phone'=>$agent->phoneNumber->number,
                'status'=>$agent->status->value,
                'forwardNumber'=>$agent->forwardNumber,
                'forwardSequence'=>$agent->forwardSequence,
                'autoResponder'=>[
                    'enabled'=>$agent->autoResponder->enabled,
                    'toneSample'=>$agent->autoResponder->toneSample,
                    'responseDelay'=>$agent->autoResponder->responseDelay->value,
                ],
            ],
        ]);
    }

    #[Route(route: '/agent/<agent>/leads', name: 'agent.leads', methods: 'GET', group: 'v1')]
    public function leads(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>$agent->leads->map(fn(Lead $item) => $this->leadView->map($item))->toArray()
        ]);
    }

    #[Route(route: '/agent/<agent>/conversation', name: 'agent.conversation', methods: 'GET', group: 'v1')]
    public function conversation(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>$agent->conversations->map(fn(Conversation $item)=>[
                    'id'=>$item->id,
                    'lead'=>$this->leadView->map($item->lead),
                    'leadPhone'=>$item->lead->phoneNumber->number,
                    'lastMessage'=>$item->messages?->last()?->content,
                    'lastMessageTime'=>$item->messages?->last()->createdAt->format('Y-m-d H:i:s'),
                    'messageCount'=>$item->messages->count(),
                    'status'=>$item->status->value,
                ])->toArray()
        ]);
    }
}
