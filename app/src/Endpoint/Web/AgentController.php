<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Agent;
use App\Database\Entity\Conversation;
use App\Database\Entity\Lead;
use App\Database\Repository\OrganizationRepository;
use App\Endpoint\Web\DataGrid\AgentGridSchema;
use App\Endpoint\Web\Filter\CreateAgentFilter;
use App\Endpoint\Web\Filter\UpdateAgentFilter;
use App\Service\AgentService;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class AgentController
{
    use PrototypeTrait;

    public function __construct(
        private readonly AgentService $agentService,
        private readonly OrganizationRepository $organizationRepository
    ) {}

    #[Route(route: '/agent', name: 'agent.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: AgentGridSchema::class)]
    public function list(): Select
    {
        return $this->agents->select();
    }

    #[Route(route: '/agent/<agent>', name: 'agent.get', methods: 'GET', group: 'v1')]
    public function get(Agent $agent): ResponseInterface
    {
        return $this->agentView->json($agent);
    }

    #[Route(route: '/agent', name: 'agent.create', methods: 'POST', group: 'v1')]
    public function create(CreateAgentFilter $filter): ResponseInterface
    {
        try {
            // Get user's organization (TODO: implement proper user context)
            $defaultOrganization = $this->organizationRepository->select()->fetchOne();
            if (!$defaultOrganization) {
                return $this->response->json([
                    'status' => 400,
                    'error' => 'No organization found'
                ], 400);
            }

            // Prepare data for agent creation
            $agentData = $filter->getAgentData();
            $data = [
                'name' => $agentData['name'],
                'status' => $agentData['status'],
                'organization' => $defaultOrganization,
            ];

            // Add optional data
            if ($phoneData = $filter->getPhoneData()) {
                $data['phone'] = $phoneData;
            }

            if ($forwardSequenceData = $filter->getForwardSequenceData()) {
                $data['forward_sequence'] = $forwardSequenceData;
            }

            if ($autoResponderData = $filter->getAutoResponderData()) {
                $data['auto_responder'] = $autoResponderData;
            }

            // Create agent
            $agent = $this->agentService->create($data, $defaultOrganization);

            return $this->response->json([
                'status' => 201,
                'message' => 'Agent created successfully',
                'data' => $this->agentView->map($agent)
            ], 201);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            $this->logger->error('Failed to create agent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/agent/<agent>', name: 'agent.update', methods: 'PATCH', group: 'v1')]
    public function update(Agent $agent, UpdateAgentFilter $filter): ResponseInterface
    {
        try {
            if (!$filter->hasUpdateData()) {
                return $this->response->json([
                    'status' => 400,
                    'error' => 'No update data provided'
                ], 400);
            }

            // Prepare data for agent update
            $data = [];

            if ($filter->name !== null) {
                $data['name'] = $filter->name;
            }

            if ($filter->getStatus() !== null) {
                $data['status'] = $filter->getStatus();
            }

            if ($phoneData = $filter->getPhoneData()) {
                $data['phone'] = $phoneData;
            }

            if (($forwardSequenceData = $filter->getForwardSequenceData()) !== null) {
                $data['forward_sequence'] = $forwardSequenceData;
            }

            if ($autoResponderData = $filter->getAutoResponderData()) {
                $data['auto_responder'] = $autoResponderData;
            }

            // Update agent
            $updatedAgent = $this->agentService->update($agent, $data);

            return $this->response->json([
                'status' => 200,
                'message' => 'Agent updated successfully',
                'data' => $this->agentView->map($updatedAgent)
            ]);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            $this->logger->error('Failed to update agent', [
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/agent/<agent>/configure', name: 'agent.configure', methods: 'GET', group: 'v1')]
    public function configure(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>[
                'id'=>$agent->id,
                'name'=>$agent->name,
                'phone'=>$agent->phoneNumber->number,
                'status'=>$agent->status->value,
                'forwardNumber'=>$agent->forwardNumber,
                'forwardSequence'=>$agent->forwardSequence,
                'autoResponder'=>[
                    'enabled'=>$agent->autoResponder->enabled,
                    'toneSample'=>$agent->autoResponder->toneSample,
                    'responseDelay'=>$agent->autoResponder->responseDelay->value,
                ],
            ],
        ]);
    }

    #[Route(route: '/agent/<agent>/leads', name: 'agent.leads', methods: 'GET', group: 'v1')]
    public function leads(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>$agent->leads->map(fn(Lead $item) => $this->leadView->map($item))->toArray()
        ]);
    }

    #[Route(route: '/agent/<agent>/conversation', name: 'agent.conversation', methods: 'GET', group: 'v1')]
    public function conversation(Agent $agent): ResponseInterface
    {
        return $this->response->json([
            'status'=>200,
            'data'=>$agent->conversations->map(fn(Conversation $item)=>[
                    'id'=>$item->id,
                    'lead'=>$this->leadView->map($item->lead),
                    'leadPhone'=>$item->lead->phoneNumber->number,
                    'lastMessage'=>$item->messages?->last()?->content,
                    'lastMessageTime'=>$item->messages?->last()->createdAt->format('Y-m-d H:i:s'),
                    'messageCount'=>$item->messages->count(),
                    'status'=>$item->status->value,
                ])->toArray()
        ]);
    }
}
