<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class PhoneNumberFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly int $number;

    #[Post]
    public readonly ?string $type;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?float $monthly_rate;

    #[Post]
    public readonly ?string $purchase_date;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'number' => [
                'required',
                ['type::int'],
                ['range', 1000000000, 99999999999] // 10-11 digit numbers
            ],
            'type' => [
                ['type::string'],
                ['in', array_column(PhoneNumberType::cases(), 'value')]
            ],
            'status' => [
                ['type::string'],
                ['in', array_column(PhoneNumberStatus::cases(), 'value')]
            ],
            'monthly_rate' => [
                ['type::float'],
                ['range', 0, null]
            ],
            'purchase_date' => [
                ['type::string'],
                ['datetime']
            ]
        ]);
    }

    /**
     * Get phone number type
     */
    public function getType(): PhoneNumberType
    {
        return PhoneNumberType::from($this->type ?? PhoneNumberType::VIRTUAL->value);
    }

    /**
     * Get phone number status
     */
    public function getStatus(): PhoneNumberStatus
    {
        return PhoneNumberStatus::from($this->status ?? PhoneNumberStatus::ACTIVE->value);
    }

    /**
     * Get validated data array
     */
    public function getData(): array
    {
        $data = [
            'number' => $this->number,
            'type' => $this->getType()->value,
            'status' => $this->getStatus()->value,
        ];

        if ($this->monthly_rate !== null) {
            $data['monthly_rate'] = $this->monthly_rate;
        }

        if ($this->purchase_date !== null) {
            $data['purchase_date'] = $this->purchase_date;
        }

        return $data;
    }
}
