<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class UpdateAgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly ?string $name;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?array $phone;

    #[Post]
    public readonly ?array $forward_sequence;

    #[Post]
    public readonly ?array $auto_responder;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'name' => [
                ['type::string'],
                ['string::longer', 2],
                ['string::shorter', 255]
            ],
            'status' => [
                ['type::string'],
                ['in', ['active', 'inactive', 'scheduled']]
            ],
            'phone' => [
                ['type::array']
            ],
            'forward_sequence' => [
                ['type::array']
            ],
            'forward_sequence.*' => [
                ['type::array']
            ],
            'auto_responder' => [
                ['type::array']
            ]
        ]);
    }

    /**
     * Get validated phone data
     */
    public function getPhoneData(): ?array
    {
        if (!$this->phone) {
            return null;
        }

        $phoneFilter = new PhoneNumberFilter($this->phone);
        return $phoneFilter->getData();
    }

    /**
     * Get validated forward sequence data
     */
    public function getForwardSequenceData(): ?array
    {
        if (!isset($this->forward_sequence)) {
            return null;
        }

        // Empty array means remove forward sequence
        if (empty($this->forward_sequence)) {
            return [];
        }

        $forwardNumbers = [];
        foreach ($this->forward_sequence as $index => $forwardData) {
            $filter = new ForwardPhoneNumberFilter($forwardData);
            $filter->validatePhoneReference();
            $forwardNumbers[] = $filter->getData();
        }

        return $forwardNumbers;
    }

    /**
     * Get validated auto responder data
     */
    public function getAutoResponderData(): ?array
    {
        if (!$this->auto_responder) {
            return null;
        }

        $autoResponderFilter = new AutoResponderFilter($this->auto_responder);
        // For updates, we don't require tone sample validation since it might be a partial update
        return $autoResponderFilter->getData();
    }

    /**
     * Get agent status
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * Check if any data is provided for update
     */
    public function hasUpdateData(): bool
    {
        return $this->name !== null ||
               $this->status !== null ||
               $this->phone !== null ||
               isset($this->forward_sequence) ||
               $this->auto_responder !== null;
    }
}
