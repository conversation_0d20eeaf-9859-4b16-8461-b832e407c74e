<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class UpdateAgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly ?string $name;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?PhoneNumberFilter $phone;

    #[Post]
    public readonly ?array $forward_sequence;

    #[Post]
    public readonly ?AutoResponderFilter $auto_responder;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'name' => [
                ['type::string'],
                ['string::longer', 2],
                ['string::shorter', 255]
            ],
            'status' => [
                ['type::string'],
                ['in', ['active', 'inactive', 'scheduled']]
            ],
            'phone' => [
                ['type::array']
            ],
            'forward_sequence' => [
                ['type::array']
            ],
            'forward_sequence.*' => [
                ['type::array']
            ],
            'auto_responder' => [
                ['type::array']
            ]
        ]);
    }

    /**
     * Get validated phone data
     */
    public function getPhoneData(): ?array
    {
        if (!$this->phone) {
            return null;
        }

        return [
            'number' => $this->phone['number'],
            'type' => $this->phone['type'] ?? PhoneNumberType::VIRTUAL->value,
            'status' => $this->phone['status'] ?? PhoneNumberStatus::ACTIVE->value,
            'monthly_rate' => $this->phone['monthly_rate'] ?? null,
            'purchase_date' => $this->phone['purchase_date'] ?? null,
        ];
    }

    /**
     * Get validated forward sequence data
     */
    public function getForwardSequenceData(): ?array
    {
        if (!isset($this->forward_sequence)) {
            return null;
        }

        // Empty array means remove forward sequence
        if (empty($this->forward_sequence)) {
            return [];
        }

        $forwardNumbers = [];
        foreach ($this->forward_sequence as $index => $forwardData) {
            $data = [
                'wait_time' => $forwardData['wait_time'] ?? 30,
            ];

            if (isset($forwardData['phone_number_id'])) {
                $data['phone_number_id'] = $forwardData['phone_number_id'];
            } elseif (isset($forwardData['number'])) {
                $data['number'] = $forwardData['number'];
                $data['type'] = $forwardData['type'] ?? PhoneNumberType::VIRTUAL->value;
                $data['status'] = $forwardData['status'] ?? PhoneNumberStatus::ACTIVE->value;
            } else {
                throw new \InvalidArgumentException("Forward sequence item {$index}: Either phone_number_id or number must be provided");
            }

            $forwardNumbers[] = $data;
        }

        return $forwardNumbers;
    }

    /**
     * Get validated auto responder data
     */
    public function getAutoResponderData(): ?array
    {
        if (!$this->auto_responder) {
            return null;
        }

        $data = [];

        if (isset($this->auto_responder['enabled'])) {
            $data['enabled'] = $this->auto_responder['enabled'];
        }

        if (isset($this->auto_responder['tone_sample'])) {
            $data['tone_sample'] = $this->auto_responder['tone_sample'];
        }

        if (isset($this->auto_responder['response_delay'])) {
            $data['response_delay'] = $this->auto_responder['response_delay'];
        }

        return $data;
    }

    /**
     * Get agent status
     */
    public function getStatus(): ?AgentStatus
    {
        return $this->status ? AgentStatus::from($this->status) : null;
    }

    /**
     * Check if any data is provided for update
     */
    public function hasUpdateData(): bool
    {
        return $this->name !== null ||
               $this->status !== null ||
               $this->phone !== null ||
               isset($this->forward_sequence) ||
               $this->auto_responder !== null;
    }
}
