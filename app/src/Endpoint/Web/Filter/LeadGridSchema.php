<?php

namespace App\Endpoint\Web\Filter;

use App\Database\Entity\Lead;
use App\Database\Enum\LeadStatus;
use App\Endpoint\Web\View\LeadView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\InArray;
use Spiral\DataGrid\Specification\Sorter\AscSorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\EnumValue;
use Spiral\DataGrid\Specification\Value\StringValue;

class LeadGridSchema extends GridSchema
{
    public function __construct(
        private readonly LeadView $leadView
    )
    {
        $this->addFilter('status', new InArray('status', new EnumValue(new StringValue(), LeadStatus::class)));
        $this->addFilter('organization', new InArray('organization.id', new StringValue()));
        $this->addFilter('agent', new InArray('agents.id', new StringValue()));

        $this->addSorter('default',new SorterSet(
            new AscSorter('status'),
            new AscSorter('name'),
        ));
    }

    public function __invoke(Lead $lead)
    {
        return $this->leadView->map($lead);
    }
}
