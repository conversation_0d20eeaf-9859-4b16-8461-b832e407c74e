<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AutoResponderDelay;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class AutoResponderFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly bool $enabled;

    #[Post]
    public readonly ?string $tone_sample;

    #[Post]
    public readonly ?string $response_delay;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'enabled' => [
                'required',
                ['type::bool']
            ],
            'tone_sample' => [
                ['type::string'],
                ['string::shorter', 1000]
            ],
            'response_delay' => [
                ['type::string'],
                ['in', array_column(AutoResponderDelay::cases(), 'value')]
            ]
        ]);
    }

    /**
     * Get response delay
     */
    public function getResponseDelay(): AutoResponderDelay
    {
        return AutoResponderDelay::from($this->response_delay ?? AutoResponderDelay::NONE->value);
    }

    /**
     * Get validated data array
     */
    public function getData(): array
    {
        $data = [
            'enabled' => $this->enabled,
            'response_delay' => $this->getResponseDelay()->value,
        ];

        if ($this->tone_sample !== null) {
            $data['tone_sample'] = $this->tone_sample;
        }

        return $data;
    }

    /**
     * Validate that tone sample is provided if enabled
     */
    public function validateToneSample(): void
    {
        if ($this->enabled && empty($this->tone_sample)) {
            throw new \InvalidArgumentException('Tone sample is required when auto responder is enabled');
        }
    }
}
