<?php

namespace App\Endpoint\Web\Filter;

use App\Database\Entity\Message;
use App\Endpoint\Web\View\MessageView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Sorter\Sorter;
use Spiral\DataGrid\Specification\Value\UuidValue;
use Spiral\Prototype\Traits\PrototypeTrait;

use Spiral\Prototype\Annotation\Prototyped;
#[Prototyped(property: 'messageGridSchema')]
class MessageGridSchema extends GridSchema
{
    public function __construct(
        private readonly MessageView $messageView
    )
    {
        $this->addFilter('conversation', new Equals('conversation.id', new UuidValue()));
        $this->addFilter('sender', new Equals('sender', new UuidValue()));

        $this->addSorter('lastMessageTime',new Sorter('createdAt'));
    }

    public function __invoke(Message $message)
    {
        return $this->messageView->map($message);
    }
}
