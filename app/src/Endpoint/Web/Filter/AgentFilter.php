<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AgentStatus;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class AgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly string $name;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?string $organization_id;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'name' => [
                'required',
                ['type::string'],
                ['string::longer', 2],
                ['string::shorter', 255]
            ],
            'status' => [
                ['type::string'],
                ['in', array_column(AgentStatus::cases(), 'value')]
            ],
            'organization_id' => [
                ['type::string'],
                ['uuid']
            ]
        ]);
    }

    /**
     * Get agent status
     */
    public function getStatus(): AgentStatus
    {
        return AgentStatus::from($this->status ?? AgentStatus::INACTIVE->value);
    }

    /**
     * Get validated data array
     */
    public function getData(): array
    {
        $data = [
            'name' => $this->name,
            'status' => $this->getStatus()->value,
        ];

        if ($this->organization_id !== null) {
            $data['organization_id'] = $this->organization_id;
        }

        return $data;
    }
}
