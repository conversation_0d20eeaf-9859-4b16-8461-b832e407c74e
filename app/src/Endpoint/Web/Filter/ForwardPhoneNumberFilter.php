<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class ForwardPhoneNumberFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly ?string $phone_number_id;

    #[Post]
    public readonly ?int $number;

    #[Post]
    public readonly ?string $type;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly int $wait_time;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'phone_number_id' => [
                ['type::string'],
                ['uuid']
            ],
            'number' => [
                ['type::int'],
                ['range', 1000000000, 99999999999]
            ],
            'type' => [
                ['type::string'],
                ['in', array_column(PhoneNumberType::cases(), 'value')]
            ],
            'status' => [
                ['type::string'],
                ['in', array_column(PhoneNumberStatus::cases(), 'value')]
            ],
            'wait_time' => [
                'required',
                ['type::int'],
                ['range', 0, 300]
            ]
        ]);
    }

    /**
     * Get phone number type
     */
    public function getType(): PhoneNumberType
    {
        return PhoneNumberType::from($this->type ?? PhoneNumberType::VIRTUAL->value);
    }

    /**
     * Get phone number status
     */
    public function getStatus(): PhoneNumberStatus
    {
        return PhoneNumberStatus::from($this->status ?? PhoneNumberStatus::ACTIVE->value);
    }

    /**
     * Get validated data array
     */
    public function getData(): array
    {
        $data = [
            'wait_time' => $this->wait_time,
        ];

        if ($this->phone_number_id !== null) {
            $data['phone_number_id'] = $this->phone_number_id;
        } elseif ($this->number !== null) {
            $data['number'] = $this->number;
            $data['type'] = $this->getType()->value;
            $data['status'] = $this->getStatus()->value;
        } else {
            throw new \InvalidArgumentException('Either phone_number_id or number must be provided');
        }

        return $data;
    }

    /**
     * Validate that either phone_number_id or number is provided
     */
    public function validatePhoneReference(): void
    {
        if ($this->phone_number_id === null && $this->number === null) {
            throw new \InvalidArgumentException('Either phone_number_id or number must be provided');
        }
    }
}
