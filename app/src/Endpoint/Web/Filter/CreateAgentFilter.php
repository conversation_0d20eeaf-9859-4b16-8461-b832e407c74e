<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AgentStatus;
use App\Database\Enum\AutoResponderDelay;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class CreateAgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly string $name;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?string $organization_id;

    #[Post]
    public readonly ?array $phone;

    #[Post]
    public readonly ?array $forward_sequence;

    #[Post]
    public readonly ?array $auto_responder;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'name' => [
                'required',
                'string',
                ['string::longer', 2],
                ['string::shorter', 255]
            ],
            'status' => [
                'string',
                ['in_array', array_column(AgentStatus::cases(), 'value'), true]
            ],
            'organization_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'phone' => ['array'],
            'phone.number' => [
                ['required', 'if' => ['present' => ['phone']]],
                'integer',
                ['number::range', 1000000000, 99999999999]
            ],
            'phone.type' => [
                'string',
                ['in_array', array_column(PhoneNumberType::cases(), 'value'), true]
            ],
            'phone.status' => [
                'string',
                ['in_array', array_column(PhoneNumberStatus::cases(), 'value'), true]
            ],
            'phone.monthly_rate' => [
                'numeric',
                ['number::higher', 0]
            ],
            'phone.purchase_date' => ['datetime'],

            'forward_sequence' => ['array'],
            'forward_sequence.*' => ['array'],
            'forward_sequence.*.phone_number_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'forward_sequence.*.number' => [
                'integer',
                ['number::range', 1000000000, 99999999999]
            ],
            'forward_sequence.*.type' => [
                'string',
                ['in_array', array_column(PhoneNumberType::cases(), 'value'), true]
            ],
            'forward_sequence.*.status' => [
                'string',
                ['in_array', array_column(PhoneNumberStatus::cases(), 'value'), true]
            ],
            'forward_sequence.*.wait_time' => [
                'required',
                'integer',
                ['number::range', 0, 300]
            ],

            'auto_responder' => ['array'],
            'auto_responder.enabled' => [
                ['required', 'if' => ['present' => ['auto_responder']]],
                'boolean'
            ],
            'auto_responder.tone_sample' => [
                ['required', 'if' => ['withAll' => ['auto_responder.enabled']]],
                'string',
                ['string::shorter', 1000]
            ],
            'auto_responder.response_delay' => [
                'string',
                ['in_array', array_column(AutoResponderDelay::cases(), 'value'), true]
            ],
        ]);
    }

    /**
     * Get validated phone data
     */
    public function getPhoneData(): ?array
    {
        if (empty($this->phone)) {
            return null;
        }

        return [
            'number' => $this->phone['number'],
            'type' => $this->phone['type'] ?? PhoneNumberType::VIRTUAL->value,
            'status' => $this->phone['status'] ?? PhoneNumberStatus::ACTIVE->value,
            'monthly_rate' => $this->phone['monthly_rate'] ?? null,
            'purchase_date' => $this->phone['purchase_date'] ?? null,
        ];
    }

    /**
     * Get validated forward sequence data
     */
    public function getForwardSequenceData(): ?array
    {
        if (empty($this->forward_sequence)) {
            return null;
        }

        $forwardNumbers = [];
        foreach ($this->forward_sequence as $index => $forwardData) {
            $data = [
                'wait_time' => $forwardData['wait_time'] ?? 30,
            ];

            if (isset($forwardData['phone_number_id'])) {
                $data['phone_number_id'] = $forwardData['phone_number_id'];
            } elseif (isset($forwardData['number'])) {
                $data['number'] = $forwardData['number'];
                $data['type'] = $forwardData['type'] ?? PhoneNumberType::VIRTUAL->value;
                $data['status'] = $forwardData['status'] ?? PhoneNumberStatus::ACTIVE->value;
            } else {
                throw new \InvalidArgumentException("Forward sequence item {$index}: Either phone_number_id or number must be provided");
            }

            $forwardNumbers[] = $data;
        }

        return $forwardNumbers;
    }

    /**
     * Get validated auto responder data
     */
    public function getAutoResponderData(): ?array
    {
        if (empty($this->auto_responder)) {
            return null;
        }

        $data = [
            'enabled' => $this->auto_responder['enabled'],
            'response_delay' => $this->auto_responder['response_delay'] ?? AutoResponderDelay::NONE->value,
        ];

        if (isset($this->auto_responder['tone_sample'])) {
            $data['tone_sample'] = $this->auto_responder['tone_sample'];
        }

        // Validate that tone sample is provided if enabled
        if ($data['enabled'] && empty($data['tone_sample'])) {
            throw new \InvalidArgumentException('Tone sample is required when auto responder is enabled');
        }

        return $data;
    }

    /**
     * Get agent status
     */
    public function getStatus(): AgentStatus
    {
        return AgentStatus::from($this->status ?? AgentStatus::INACTIVE->value);
    }
}
