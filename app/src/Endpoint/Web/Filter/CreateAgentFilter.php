<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AgentStatus;
use App\Database\Enum\AutoResponderDelay;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class CreateAgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly AgentFilter $agent;

    #[Post]
    public readonly ?PhoneNumberFilter $phone;

    #[Post]
    public readonly ?array $forward_sequence;

    #[Post]
    public readonly ?AutoResponderFilter $auto_responder;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'agent' => [
                'required',
                ['type::array']
            ],
            'phone' => [
                ['type::array']
            ],
            'forward_sequence' => [
                ['type::array']
            ],
            'forward_sequence.*' => [
                ['type::array']
            ],
            'auto_responder' => [
                ['type::array']
            ]
        ]);
    }

    /**
     * Get validated agent data
     */
    public function getAgentData(): array
    {
        return $this->agent->getData();
    }

    /**
     * Get validated phone data
     */
    public function getPhoneData(): ?array
    {
        return $this->phone?->getData();
    }

    /**
     * Get validated forward sequence data
     */
    public function getForwardSequenceData(): ?array
    {
        if (!$this->forward_sequence) {
            return null;
        }

        $forwardNumbers = [];
        foreach ($this->forward_sequence as $index => $forwardData) {
            $filter = new ForwardPhoneNumberFilter($forwardData);
            $filter->validatePhoneReference();
            $forwardNumbers[] = $filter->getData();
        }

        return $forwardNumbers;
    }

    /**
     * Get validated auto responder data
     */
    public function getAutoResponderData(): ?array
    {
        if (!$this->auto_responder) {
            return null;
        }

        $this->auto_responder->validateToneSample();
        return $this->auto_responder->getData();
    }
}
