<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AgentStatus;
use App\Database\Enum\AutoResponderDelay;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class CreateAgentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly string $name;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?string $organization_id;

    #[Post]
    public readonly ?array $phone;

    #[Post]
    public readonly ?array $forward_sequence;

    #[Post]
    public readonly ?array $auto_responder;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'name' => [
                'required',
                ['type::string'],
                ['string::longer', 2],
                ['string::shorter', 255]
            ],
            'status' => [
                ['type::string'],
                ['in', ['active', 'inactive', 'scheduled']]
            ],
            'organization_id' => [
                ['type::string'],
                ['uuid']
            ],
            'phone' => [
                ['type::array']
            ],
            'forward_sequence' => [
                ['type::array']
            ],
            'forward_sequence.*' => [
                ['type::array']
            ],
            'auto_responder' => [
                ['type::array']
            ]
        ]);
    }

    /**
     * Get validated agent data
     */
    public function getAgentData(): array
    {
        $agentFilter = new AgentFilter([
            'name' => $this->name,
            'status' => $this->status,
            'organization_id' => $this->organization_id
        ]);

        return $agentFilter->getData();
    }

    /**
     * Get validated phone data
     */
    public function getPhoneData(): ?array
    {
        if (!$this->phone) {
            return null;
        }

        $phoneFilter = new PhoneNumberFilter($this->phone);
        return $phoneFilter->getData();
    }

    /**
     * Get validated forward sequence data
     */
    public function getForwardSequenceData(): ?array
    {
        if (!$this->forward_sequence) {
            return null;
        }

        $forwardNumbers = [];
        foreach ($this->forward_sequence as $index => $forwardData) {
            $filter = new ForwardPhoneNumberFilter($forwardData);
            $filter->validatePhoneReference();
            $forwardNumbers[] = $filter->getData();
        }

        return $forwardNumbers;
    }

    /**
     * Get validated auto responder data
     */
    public function getAutoResponderData(): ?array
    {
        if (!$this->auto_responder) {
            return null;
        }

        $autoResponderFilter = new CreateAutoResponderFilter($this->auto_responder);
        $autoResponderFilter->validateToneSample();
        return $autoResponderFilter->getData();
    }
}
