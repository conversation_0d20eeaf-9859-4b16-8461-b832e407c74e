<?php

namespace App\Endpoint\Web;

use App\Endpoint\Web\DataGrid\ConversationGridSchema;
use Cycle\ORM\Select;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

class ConversationController
{
    use PrototypeTrait;

    #[Route(route: '/conversation?limit=<limit:\d+>', name: 'conversation.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: ConversationGridSchema::class)]
    public function list(): Select
    {
        return $this->conversations->select();
    }
}
