<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Conversation;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'conversationView')]
class ConversationView
{
    use PrototypeTrait;

    public function map(Conversation $conversation): array
    {
        return [
            'id' => $conversation->id,
            'lead' => $conversation->lead->id,
            'agent' => $conversation->agent->id,
//            'messages'=>$conversation->messages->map(function($item) {
//                $this->messageView->map($item);
//            })->toArray(),
//            'lead' => [
//                'id' => $conversation->lead->id,
//                'person' => [
//                    'id' => $conversation->lead->person->id,
//                    'name' => [
//                        'full' => $conversation->lead->person->firstName.' '.$conversation->lead->person->lastName,
//                        'first' => $conversation->lead->person->firstName,
//                        'last' => $conversation->lead->person->lastName,
//                    ]
//                ],
//                'phonenumber' => $conversation->lead->phoneNumber->number,
//            ],
//            'agent' => [
//                'id' => $conversation->agent->id,
//                'name' => $conversation->agent->name,
//            ],
            'lastMessage' => $conversation->messages->last()->content,
            'lastMessageTime' => $conversation->messages->last()->createdAt->format('Y-m-d H:i:s'),
            'messageCount' => $conversation->messages->count(),
            'status' => $conversation->status->value,
            'metadata' => [
                'createdAt' => [
                    'raw' => $conversation->createdAt ?? null,
                    'formatted' => Carbon::createFromImmutable($conversation->createdAt)->diffForHumans(),
                ],
                'createdBy' => 'Unknown', // @todo
                'updatedAt' => [
                    'raw' => $conversation->updatedAt ?? null,
                    'formatted' => $conversation->updatedAt ? Carbon::createFromImmutable($conversation->updatedAt)->diffForHumans()
                    : 'Never',
                ],
                'updatedBy' => 'Unknown', // @todo
            ]
        ];
    }

    public function json(Conversation $conversation): ResponseInterface
    {
        return $this->response->json($this->map($conversation), 200);
    }
}
