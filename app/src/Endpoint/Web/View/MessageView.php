<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Message;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'messageView')]
class MessageView
{
    use PrototypeTrait;
    public function map(Message $message): array
    {
        return [
            'id' => $message->id,
            'conversation' => $message->conversation->id,
            'content' => $message->content,
            'sender' => $message->sender->value,
            'createdAt' => $message->createdAt->format('Y-m-d H:i:s'),
        ];
    }

    public function json(Message $message): ResponseInterface
    {
        return $this->response->json($this->map($message), 200);
    }
}
