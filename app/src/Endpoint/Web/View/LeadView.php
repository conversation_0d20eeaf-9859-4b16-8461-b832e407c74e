<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Lead;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'leadView')]
class LeadView
{
    use PrototypeTrait;

    public function map(Lead $lead)
    {
        return [
            'id' => $lead->id,
            'person' => $lead->person,
            'organization' => $lead->organization->id,
            'service' => $lead->service,
            'status' => $lead->status->value,
            'priority' => $lead->priority->value,
            'estimatedValue' => $lead->estimatedValue,
            'actualValue' => $lead->actualValue,
            'estimatedCost' => $lead->estimatedCost,
            'lastContact' => $lead->lastContact,
            'score' => $lead->score,
        ];
    }

    public function json(Lead $lead)
    {
        return $this->response->json($this->map($lead), 200);
    }
}
