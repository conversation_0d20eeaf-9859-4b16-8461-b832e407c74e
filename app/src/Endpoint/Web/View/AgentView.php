<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Agent;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Http\ResponseWrapper;

#[Singleton]
#[Prototyped(property: 'agentView')]
class AgentView
{
    use PrototypeTrait;

    public function map(Agent $agent): array
    {

        $forwardNumber = $agent->forwardSequence?->forwardPhoneNumbers->first()->phoneNumber->number;
        $forwardSequence = $agent->forwardSequence?->forwardPhoneNumbers->map(fn($item) => [
            'number' => $item->phoneNumber->number,
            'waitTime' => $item->waitTime,
        ])->toArray();

        return [
            'id' => $agent->id,
            'name' => $agent->name,
            'status' => $agent->status->value,
            'phone' => $agent->phoneNumber->number,
            'forwardNumber' => $forwardNumber,
            'forwardSequence' => $forwardSequence,
            'organization' => [
                'id' => $agent->organization->id,
                'name' => $agent->organization->name,
            ],
            'autoResponder' => ($agent->autoResponder !== null) ? [
                'enabled' => $agent->autoResponder?->enabled,
                'toneSample' => $agent->autoResponder?->toneSample,
                'responseDelay' => $agent->autoResponder?->responseDelay->value,
            ] : null,
            'metrics' => [
                'avgLeadResponseTime' => $agent->calculateAvgLeadResponseTime(),
                'leadResponseRate' => $agent->calculateLeadResponseRate(),
                'callsHandled' => $agent->calculateCallsHandled(),
                'voicemailCount' => $agent->calculateVoicemailCount(),
                'conversationsHandled' => $agent->calculateConversationCount(),
                'conversionCount' => $agent->calculateConversionCount(),
                'conversionRate' => $agent->calculateConversionRate(),
                'avgCostPerConversion' => $agent->calculateAverageCostPerConversion(),
                'avgConversionTime' => $agent->calculateAvgConversionTime(),
                'avgLeadSentiment' => $agent->calculateAvgLeadSentiment(),
                'totalMonthlyCost' => $agent->calculateTotalMonthlyCost(),
                'messageCount' => $agent->calculateMessageCount(),
                'avgMessagePerConversation' => $agent->calculateAvgMessagePerConversation(),
                'smsReputation' => $agent->calculateSmsReputation(),
                'lastActivity' => $agent->calculateLastActivity(),
            ],
            'metadata' => [
                'createdAt' => [
                    'raw' => $agent->createdAt ?? null,
                    'formatted' => Carbon::createFromImmutable($agent->createdAt)->diffForHumans(),
                ],
                'createdBy' => 'Unknown', // @todo
                'updatedAt' => [
                    'raw' => $agent->updatedAt ?? null,
                    'formatted' => $agent->updatedAt ? Carbon::createFromImmutable($agent->updatedAt)->diffForHumans() : 'Never',
                ],
                'updatedBy' => 'Unknown', // @todo
            ]
        ];
    }

    public function json(Agent $agent): ResponseInterface
    {
        return $this->response->json($this->map($agent), 200);
    }
}
