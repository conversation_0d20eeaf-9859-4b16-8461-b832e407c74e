<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Agent;
use Cycle\ORM\Select;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

class LeadController
{
    use PrototypeTrait;

    #[Route(route: '/lead', name: 'lead.list', methods: 'GET', group: 'v1')]
    public function list(): Select
    {
        return $this->leads->select();
    }

    #[Route(route: '/lead/<lead>', name: 'lead.get', methods: 'GET', group: 'v1')]
    public function get(Lead $lead)
    {
        return $this->leadView->json($lead);
    }
}
