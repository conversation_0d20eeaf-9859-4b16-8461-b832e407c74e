<?php

declare(strict_types=1);

namespace App\Endpoint\Web;

use App\Application\Exception\ConflictException;
use App\Service\AuthService;
use App\Service\UserRegistrationService;
use Psr\Http\Message\ResponseInterface;
use Spiral\Http\Exception\ClientException\BadRequestException;
use Spiral\Http\Exception\ClientException\UnauthorizedException;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class AuthController
{
    use PrototypeTrait;

    public function __construct(
        private readonly AuthService $authService,
        private readonly UserRegistrationService $userRegistrationService
    ) {}

    /**
     * Login endpoint that validates credentials and returns JWT token
     */
    #[Route(route: '/auth/login_check', name: 'auth.login_check', methods: 'POST')]
    public function loginCheck(): ResponseInterface
    {
        $username = $this->input->post('username');
        $password = $this->input->post('password');

        if (!$username || !$password) {
            throw new BadRequestException('Username and password are required');
        }

        $username = trim($username);
        $password = trim($password);

        if (empty($username) || empty($password)) {
            throw new BadRequestException('Username and password cannot be empty');
        }

        $user = $this->authService->authenticate($username, $password);

        if (!$user) {
            throw new UnauthorizedException('Invalid credentials');
        }

        $token = $this->authService->generateToken($user);

        return $this->response->json([
            'success' => true,
            'token' => $token,
            'user' => [
                'id' => $user->id->toString(),
                'username' => $user->username,
                'verified' => $user->verified,
                'person' => [
                    'firstName' => $user->person->firstName ?? null,
                    'lastName' => $user->person->lastName ?? null,
                    'email' => $user->person->email ?? null,
                ]
            ],
            'expires_in' => 3600 * 24 * 7 // 7 days in seconds
        ]);
    }

    /**
     * Validate token endpoint
     */
    #[Route(route: '/auth/validate', name: 'auth.validate', methods: 'POST')]
    public function validate(): ResponseInterface
    {
        $authHeader = $this->request->headers->get('Authorization');

        if (!$authHeader) {
            throw new BadRequestException('Authorization header is required');
        }

        $token = $this->authService->extractTokenFromHeader($authHeader);

        if (!$token) {
            throw new BadRequestException('Invalid authorization header format');
        }

        $payload = $this->authService->validateToken($token);

        if (!$payload) {
            return $this->response->json([
                'valid' => false,
                'message' => 'Invalid or expired token'
            ], 401);
        }

        $user = $this->authService->getUserFromToken($token);

        if (!$user) {
            return $this->response->json([
                'valid' => false,
                'message' => 'User not found'
            ], 401);
        }

        return $this->response->json([
            'valid' => true,
            'user' => [
                'id' => $user->id->toString(),
                'username' => $user->username,
                'verified' => $user->verified,
                'person' => [
                    'firstName' => $user->person->firstName ?? null,
                    'lastName' => $user->person->lastName ?? null,
                    'email' => $user->person->email ?? null,
                ]
            ],
            'expires_at' => $payload['exp'] ?? null
        ]);
    }

    /**
     * Refresh token endpoint
     */
    #[Route(route: '/auth/refresh', name: 'auth.refresh', methods: 'POST')]
    public function refresh(): ResponseInterface
    {
        $authHeader = $this->request->getHeaderLine('Authorization');

        if (!$authHeader) {
            throw new BadRequestException('Authorization header is required');
        }

        $token = $this->authService->extractTokenFromHeader($authHeader);

        if (!$token) {
            throw new BadRequestException('Invalid authorization header format');
        }

        $newToken = $this->authService->refreshToken($token);

        if (!$newToken) {
            throw new UnauthorizedException('Unable to refresh token');
        }

        return $this->response->json([
            'success' => true,
            'token' => $newToken,
            'expires_in' => 3600 * 24 * 7 // 7 days in seconds
        ]);
    }

    /**
     * Logout endpoint (client-side token invalidation)
     */
    #[Route(route: '/auth/logout', name: 'auth.logout', methods: 'POST')]
    public function logout(): ResponseInterface
    {
        // Since JWT is stateless, logout is handled client-side by removing the token
        // This endpoint exists for consistency and future token blacklisting if needed
        return $this->response->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * User registration endpoint
     */
    #[Route(route: '/auth/register', name: 'auth.register', methods: 'POST')]
    public function register(): ResponseInterface
    {
        $firstName = $this->input->post('firstName');
        $lastName = $this->input->post('lastName');
        $email = $this->input->post('email');
        $password = $this->input->post('password');

        // Validate required fields
        if (!$firstName || !$lastName || !$email || !$password) {
            throw new BadRequestException('All fields (firstName, lastName, email, password) are required');
        }

        $firstName = trim($firstName);
        $lastName = trim($lastName);
        $email = trim($email);
        $password = trim($password);

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new BadRequestException('Invalid email address format');
        }

        // Validate password strength (minimum 8 characters)
        if (strlen($password) < 8) {
            throw new BadRequestException('Password must be at least 8 characters long');
        }

        try {
            $result = $this->userRegistrationService->registerUser(
                $firstName,
                $lastName,
                $email,
                $password
            );

            return $this->response->json([
                'success' => true,
                'message' => $result['message'],
                'user_id' => $result['user']->id->toString(),
                'requires_verification' => !$result['user']->verified,
                'needs_organization_setup' => $result['needs_organization_setup'] ?? false
            ]);

        } catch (ConflictException $e) {
            // Handle duplicate email scenarios
            return $this->response->json([
                'success' => false,
                'error' => 'email_exists',
                'message' => $e->getMessage()
            ], 409);
        }
    }
}
