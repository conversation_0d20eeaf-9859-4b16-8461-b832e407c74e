<?php

declare(strict_types=1);

namespace App\Service;

use App\Application\Exception\ConflictException;
use App\Database\Entity\Person;
use App\Database\Entity\User;
use App\Database\Repository\PersonRepository;
use App\Database\Repository\UserRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'userRegistrationService')]
class UserRegistrationService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly PersonRepository $personRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Register a new user with person record
     */
    public function registerUser(
        string $firstName,
        string $lastName,
        string $email,
        string $password
    ): array {
        // Check if email already exists
        $existingUser = $this->userRepository->findByEmail($email);

        if ($existingUser) {
            if ($existingUser->verified) {
                // User exists and is verified - suggest password reset
                throw new ConflictException(
                    'An account with this email address already exists. Please try logging in or use password reset if you forgot your password.'
                );
            } else {
                // User exists but not verified - resend verification
                // TODO: Implement email verification resend
                throw new ConflictException(
                    'An account with this email address exists but is not verified. A new verification email has been sent.'
                );
            }
        }

        // Create person record first
        $person = new Person();
        $person->firstName = $firstName;
        $person->lastName = $lastName;
        $person->email = $email;

        // Persist person to get ID
        $this->entityManager->persist($person);
        $this->entityManager->run();

        // Create user record without organization (will be set up later via wizard)
        $user = new User();
        $user->username = $email; // Use email as username
        $user->password = password_hash($password, PASSWORD_BCRYPT);
        $user->verified = true; // TODO: Set to false and implement email verification
        $user->person = $person;
        $user->organization = null; // No organization initially - will be created via setup wizard

        // Persist user
        $this->entityManager->persist($user);
        $this->entityManager->run();

        return [
            'user' => $user,
            'person' => $person,
            'needs_organization_setup' => true, // User will need to complete organization setup
            'message' => $user->verified
                ? 'Account created successfully. You can now log in.'
                : 'Account created successfully. Please check your email to verify your account.'
        ];
    }

    /**
     * Check if email is already in use
     */
    public function isEmailInUse(string $email): bool
    {
        return $this->userRepository->findByEmail($email) !== null;
    }

    /**
     * Get user by email (including unverified users)
     */
    public function getUserByEmail(string $email): ?User
    {
        return $this->userRepository->findByEmail($email);
    }

    /**
     * Resend verification email (placeholder for future implementation)
     */
    public function resendVerificationEmail(User $user): bool
    {
        // TODO: Implement email verification system
        // This would:
        // 1. Generate a verification token
        // 2. Store it in database or cache
        // 3. Send email with verification link
        // 4. Return success/failure status

        return true; // Placeholder return
    }

    /**
     * Verify user email with token (placeholder for future implementation)
     */
    public function verifyEmail(string $userId, string $token): bool
    {
        // TODO: Implement email verification
        // This would:
        // 1. Find user by ID
        // 2. Validate verification token
        // 3. Mark user as verified
        // 4. Clean up verification token

        $user = $this->userRepository->findByPK($userId);
        if (!$user) {
            return false;
        }

        // For now, just mark as verified
        $user->verified = true;
        $this->entityManager->persist($user);
        $this->entityManager->run();

        return true;
    }



    /**
     * Generate verification token (placeholder for future implementation)
     */
    private function generateVerificationToken(): string
    {
        return bin2hex(random_bytes(32));
    }
}
