<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Agent;
use App\Database\Entity\Organization;
use App\Database\Entity\PhoneNumber;
use App\Database\Enum\AgentStatus;
use App\Database\Repository\AgentRepository;
use App\Database\Repository\OrganizationRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'agentService')]
class AgentService
{
    public function __construct(
        private readonly AgentRepository $agentRepository,
        private readonly OrganizationRepository $organizationRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation,
        private readonly PhoneNumberService $phoneNumberService,
        private readonly ForwardSequenceService $forwardSequenceService,
        private readonly AutoResponderService $autoResponderService
    ) {}

    /**
     * Create a new agent
     */
    public function create(array $data, Organization $defaultOrganization): Agent
    {
        $this->validateCreateData($data);

        $agent = new Agent();
        $agent->name = $data['name'];
        $agent->status = AgentStatus::from($data['status'] ?? AgentStatus::INACTIVE->value);
        $agent->organization = $data['organization'] ?? $defaultOrganization;

        // Handle phone number
        if (isset($data['phone'])) {
            $agent->phoneNumber = $this->handlePhoneNumber($data['phone']);
        }

        $this->entityManager->persist($agent);
        $this->entityManager->run();

        // Handle forward sequence
        if (isset($data['forward_sequence']) && !empty($data['forward_sequence'])) {
            $this->forwardSequenceService->create($agent, $data['forward_sequence']);
        }

        // Handle auto responder
        if (isset($data['auto_responder'])) {
            $this->autoResponderService->create($agent, $data['auto_responder']);
        }

        return $agent;
    }

    /**
     * Update an existing agent
     */
    public function update(Agent $agent, array $data): Agent
    {
        $this->validateUpdateData($data);

        // Update basic fields
        if (isset($data['name'])) {
            $agent->name = $data['name'];
        }

        if (isset($data['status'])) {
            $agent->status = AgentStatus::from($data['status']);
        }

        // Handle phone number update
        if (isset($data['phone'])) {
            $agent->phoneNumber = $this->handlePhoneNumber($data['phone']);
        }

        $this->entityManager->persist($agent);
        $this->entityManager->run();

        // Handle forward sequence updates
        if (isset($data['forward_sequence'])) {
            if (empty($data['forward_sequence'])) {
                // Remove forward sequence if empty array provided
                if ($agent->forwardSequence) {
                    $this->forwardSequenceService->delete($agent->forwardSequence);
                }
            } else {
                // Update or create forward sequence
                if ($agent->forwardSequence) {
                    $this->forwardSequenceService->update($agent->forwardSequence, $data['forward_sequence']);
                } else {
                    $this->forwardSequenceService->create($agent, $data['forward_sequence']);
                }
            }
        }

        // Handle auto responder updates
        if (isset($data['auto_responder'])) {
            if ($agent->autoResponder) {
                $this->autoResponderService->update($agent->autoResponder, $data['auto_responder']);
            } else {
                $this->autoResponderService->create($agent, $data['auto_responder']);
            }
        }

        return $agent;
    }

    /**
     * Delete an agent (soft delete)
     */
    public function delete(Agent $agent): void
    {
        $this->entityManager->delete($agent);
        $this->entityManager->run();
    }

    /**
     * Activate an agent
     */
    public function activate(Agent $agent): Agent
    {
        $agent->status = AgentStatus::ACTIVE;
        $this->entityManager->persist($agent);
        $this->entityManager->run();

        return $agent;
    }

    /**
     * Deactivate an agent
     */
    public function deactivate(Agent $agent): Agent
    {
        $agent->status = AgentStatus::INACTIVE;
        $this->entityManager->persist($agent);
        $this->entityManager->run();

        return $agent;
    }

    /**
     * Get agent with all relationships loaded
     */
    public function getAgentWithRelationships(string $agentId): ?Agent
    {
        return $this->agentRepository->getAgentWithAll($agentId);
    }

    /**
     * Handle phone number creation or assignment
     */
    private function handlePhoneNumber(array|string|int $phoneData): ?PhoneNumber
    {
        if (is_string($phoneData) || is_int($phoneData)) {
            // Simple phone number string/int - find existing or create new
            $number = (int) $phoneData;
            $existingPhone = $this->phoneNumberService->findByNumber($number);
            
            if ($existingPhone) {
                return $existingPhone;
            }

            return $this->phoneNumberService->create([
                'number' => $number,
                'type' => 'virtual',
                'status' => 'active'
            ]);
        }

        if (is_array($phoneData)) {
            // Phone data array - create new phone number
            return $this->phoneNumberService->create($phoneData);
        }

        return null;
    }

    /**
     * Validate create data
     */
    private function validateCreateData(array $data): void
    {
        $rules = [
            'name' => ['required', 'string', 'min:2', 'max:255'],
            'status' => ['string', 'in:' . implode(',', array_column(AgentStatus::cases(), 'value'))],
            'organization' => ['object'], // Organization entity
            'phone' => ['array|string|integer'],
            'forward_sequence' => ['array'],
            'auto_responder' => ['array']
        ];

        $validator = $this->validation->validate($data, $rules);
        
        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }

        // Check if agent name already exists in organization
        $existingAgent = $this->agentRepository->select()
            ->where('name', $data['name'])
            ->where('organization.id', $data['organization']?->id ?? null)
            ->fetchOne();

        if ($existingAgent) {
            throw new \InvalidArgumentException('Agent with this name already exists in the organization');
        }
    }

    /**
     * Validate update data
     */
    private function validateUpdateData(array $data): void
    {
        $rules = [
            'name' => ['string', 'min:2', 'max:255'],
            'status' => ['string', 'in:' . implode(',', array_column(AgentStatus::cases(), 'value'))],
            'phone' => ['array|string|integer'],
            'forward_sequence' => ['array'],
            'auto_responder' => ['array']
        ];

        $validator = $this->validation->validate($data, $rules);
        
        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }
}
