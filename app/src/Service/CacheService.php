<?php

namespace App\Service;

use Psr\SimpleCache\CacheInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'cacheService')]
#[Singleton]
class CacheService
{
    private static ?CacheService $instance = null;

    public function __construct(
        private readonly CacheInterface $cache
    )
    {
        self::$instance = $this;
    }

    public static function instance(): CacheService
    {
        if (self::$instance === null) {
            throw new \RuntimeException('CacheService not initialized');
        }
        return self::$instance;
    }

    public function augmentCache($key, $calculation, $ttl = 60)
    {
        $value = $this->cache->get($key);

        if ($value !== null) {
            return $value;
        }

        $value = $calculation();
        $this->cache->set(
            key: $key,
            value: $value,
            ttl: $ttl
        );
        return $value;
    }
}
