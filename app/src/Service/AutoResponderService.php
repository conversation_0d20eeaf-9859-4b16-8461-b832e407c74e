<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Agent;
use App\Database\Entity\AutoResponder;
use App\Database\Enum\AutoResponderDelay;
use Cycle\ORM\EntityManagerInterface;
use <PERSON><PERSON>ral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'autoResponderService')]
class AutoResponderService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation
    ) {}

    /**
     * Create a new auto responder for an agent
     */
    public function create(Agent $agent, array $data): AutoResponder
    {
        $this->validateCreateData($data);

        $autoResponder = new AutoResponder();
        $autoResponder->agent = $agent;
        $autoResponder->enabled = $data['enabled'];
        $autoResponder->responseDelay = AutoResponderDelay::from($data['response_delay'] ?? AutoResponderDelay::NONE->value);

        // Tone sample is required if enabled
        if ($autoResponder->enabled) {
            if (empty($data['tone_sample'])) {
                throw new \InvalidArgumentException('Tone sample is required when auto responder is enabled');
            }
            $autoResponder->toneSample = $data['tone_sample'];
        } else {
            $autoResponder->toneSample = $data['tone_sample'] ?? '';
        }

        $this->entityManager->persist($autoResponder);
        $this->entityManager->run();

        return $autoResponder;
    }

    /**
     * Update an existing auto responder
     */
    public function update(AutoResponder $autoResponder, array $data): AutoResponder
    {
        $this->validateUpdateData($data);

        if (isset($data['enabled'])) {
            $autoResponder->enabled = $data['enabled'];
        }

        if (isset($data['tone_sample'])) {
            $autoResponder->toneSample = $data['tone_sample'];
        }

        if (isset($data['response_delay'])) {
            $autoResponder->responseDelay = AutoResponderDelay::from($data['response_delay']);
        }

        // Validate that tone sample is provided if enabled
        if ($autoResponder->enabled && empty($autoResponder->toneSample)) {
            throw new \InvalidArgumentException('Tone sample is required when auto responder is enabled');
        }

        $this->entityManager->persist($autoResponder);
        $this->entityManager->run();

        return $autoResponder;
    }

    /**
     * Enable auto responder
     */
    public function enable(AutoResponder $autoResponder, string $toneSample): AutoResponder
    {
        if (empty($toneSample)) {
            throw new \InvalidArgumentException('Tone sample is required to enable auto responder');
        }

        $autoResponder->enabled = true;
        $autoResponder->toneSample = $toneSample;

        $this->entityManager->persist($autoResponder);
        $this->entityManager->run();

        return $autoResponder;
    }

    /**
     * Disable auto responder
     */
    public function disable(AutoResponder $autoResponder): AutoResponder
    {
        $autoResponder->enabled = false;

        $this->entityManager->persist($autoResponder);
        $this->entityManager->run();

        return $autoResponder;
    }

    /**
     * Delete auto responder
     */
    public function delete(AutoResponder $autoResponder): void
    {
        $this->entityManager->delete($autoResponder);
        $this->entityManager->run();
    }

    /**
     * Get available response delay options
     */
    public function getResponseDelayOptions(): array
    {
        return [
            AutoResponderDelay::NONE->value => '0 seconds',
            AutoResponderDelay::VERY_SHORT->value => '1-30 seconds',
            AutoResponderDelay::SHORT->value => '30-60 seconds',
            AutoResponderDelay::MEDIUM->value => '1-5 minutes',
            AutoResponderDelay::LONG->value => '5-10 minutes',
            AutoResponderDelay::VERY_LONG->value => '10-30 minutes',
            AutoResponderDelay::EXTREMELY_LONG->value => '30 minutes - 1 hour',
            AutoResponderDelay::RANDOM->value => 'Random (1-15 minutes)',
        ];
    }

    /**
     * Validate create data
     */
    private function validateCreateData(array $data): void
    {
        $rules = [
            'enabled' => ['required', 'boolean'],
            'tone_sample' => ['string', 'max:1000'],
            'response_delay' => ['string', 'in:' . implode(',', array_column(AutoResponderDelay::cases(), 'value'))],
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }

    /**
     * Validate update data
     */
    private function validateUpdateData(array $data): void
    {
        $rules = [
            'enabled' => ['boolean'],
            'tone_sample' => ['string', 'max:1000'],
            'response_delay' => ['string', 'in:' . implode(',', array_column(AutoResponderDelay::cases(), 'value'))],
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }
}
