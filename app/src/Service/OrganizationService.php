<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Organization;
use App\Database\Entity\Industry;
use App\Database\Entity\Address;
use App\Database\Entity\PhoneNumber;
use App\Database\Entity\Person;
use App\Database\Enum\PhoneNumberType;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Repository\OrganizationRepository;
use App\Database\Repository\IndustryRepository;
use App\Database\Repository\AddressRepository;
use App\Database\Repository\PhoneNumberRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'organizationService')]
class OrganizationService
{
    public function __construct(
        private readonly OrganizationRepository $organizationRepository,
        private readonly IndustryRepository $industryRepository,
        private readonly AddressRepository $addressRepository,
        private readonly PhoneNumberRepository $phoneNumberRepository,
        private readonly EntityManagerInterface $entityManager
    ) {}

    /**
     * Create a new organization with provided details
     */
    public function createOrganization(
        string $name,
        Industry $industry,
        Address $address,
        PhoneNumber $phoneNumber
    ): Organization {
        $organization = new Organization();
        $organization->name = $name;
        $organization->industry = $industry;
        $organization->address = $address;
        $organization->phoneNumber = $phoneNumber;

        $this->entityManager->persist($organization);
        $this->entityManager->run();

        return $organization;
    }

    /**
     * Create a new organization with placeholder data for setup wizard
     */
    public function createOrganizationWithPlaceholders(string $name): Organization
    {
        // Create placeholder industry
        $industry = $this->getOrCreatePlaceholderIndustry();

        // Create placeholder address
        $address = $this->createPlaceholderAddress();

        // Create placeholder phone number
        $phoneNumber = $this->createPlaceholderPhoneNumber();

        return $this->createOrganization($name, $industry, $address, $phoneNumber);
    }

    /**
     * Get or create a placeholder industry record
     */
    private function getOrCreatePlaceholderIndustry(): Industry
    {
        // Check if a placeholder industry already exists
        $existingIndustry = $this->industryRepository->findOne(['name' => 'To Be Determined']);

        if ($existingIndustry) {
            return $existingIndustry;
        }

        // Create new placeholder industry
        $industry = new Industry();
        $industry->name = 'To Be Determined';
        $industry->description = 'Placeholder industry to be updated during organization setup';

        $this->entityManager->persist($industry);
        $this->entityManager->run();

        return $industry;
    }

    /**
     * Create a placeholder address record
     */
    private function createPlaceholderAddress(): Address
    {
        $address = new Address();
        $address->street1 = 'To Be Determined';
        $address->city = 'To Be Determined';
        $address->state = 'TBD';
        $address->zip = '00000';

        $this->entityManager->persist($address);
        $this->entityManager->run();

        return $address;
    }

    /**
     * Create a placeholder phone number record
     */
    private function createPlaceholderPhoneNumber(): PhoneNumber
    {
        $phoneNumber = new PhoneNumber();
        $phoneNumber->number = 0000000000; // Placeholder number
        $phoneNumber->type = PhoneNumberType::VIRTUAL;
        $phoneNumber->status = PhoneNumberStatus::PENDING;
        $phoneNumber->monthlyRate = null;
        $phoneNumber->purchaseDate = null;

        $this->entityManager->persist($phoneNumber);
        $this->entityManager->run();

        return $phoneNumber;
    }

    /**
     * Update organization details
     */
    public function updateOrganization(
        Organization $organization,
        string $name,
        ?Industry $industry = null,
        ?Address $address = null,
        ?PhoneNumber $phoneNumber = null
    ): Organization {
        $organization->name = $name;

        if ($industry) {
            $organization->industry = $industry;
        }

        if ($address) {
            $organization->address = $address;
        }

        if ($phoneNumber) {
            $organization->phoneNumber = $phoneNumber;
        }

        $this->entityManager->persist($organization);
        $this->entityManager->run();

        return $organization;
    }

    /**
     * Check if organization has placeholder data
     */
    public function hasPlaceholderData(Organization $organization): bool
    {
        return $organization->industry->name === 'To Be Determined' ||
               $organization->address->street1 === 'To Be Determined' ||
               $organization->phoneNumber->number === 0000000000;
    }

    /**
     * Get organization setup completion percentage
     */
    public function getSetupCompletionPercentage(Organization $organization): int
    {
        $totalFields = 4; // name, industry, address, phone
        $completedFields = 0;

        // Name is always set during creation
        $completedFields++;

        // Check if industry is not placeholder
        if ($organization->industry->name !== 'To Be Determined') {
            $completedFields++;
        }

        // Check if address is not placeholder
        if ($organization->address->street1 !== 'To Be Determined') {
            $completedFields++;
        }

        // Check if phone number is not placeholder
        if ($organization->phoneNumber->number !== 0000000000) {
            $completedFields++;
        }

        return (int) round(($completedFields / $totalFields) * 100);
    }

    /**
     * Get organizations that need setup completion
     */
    public function getOrganizationsNeedingSetup(): array
    {
        // This would typically use a more complex query
        // For now, we'll get all organizations and filter in PHP
        $allOrganizations = $this->organizationRepository->select()->fetchAll();

        return array_filter($allOrganizations, function (Organization $org) {
            return $this->hasPlaceholderData($org);
        });
    }

    /**
     * Create a new industry
     */
    public function createIndustry(string $name, string $description): Industry
    {
        $industry = new Industry();
        $industry->name = $name;
        $industry->description = $description;

        $this->entityManager->persist($industry);
        $this->entityManager->run();

        return $industry;
    }

    /**
     * Create a new address
     */
    public function createAddress(
        string $street1,
        string $city,
        string $state,
        string $zip,
        ?string $street2 = null
    ): Address {
        $address = new Address();
        $address->street1 = $street1;
        $address->street2 = $street2;
        $address->city = $city;
        $address->state = $state;
        $address->zip = $zip;

        $this->entityManager->persist($address);
        $this->entityManager->run();

        return $address;
    }

    /**
     * Create a new phone number
     */
    public function createPhoneNumber(
        int $number,
        PhoneNumberType $type = PhoneNumberType::VIRTUAL,
        PhoneNumberStatus $status = PhoneNumberStatus::ACTIVE
    ): PhoneNumber {
        $phoneNumber = new PhoneNumber();
        $phoneNumber->number = $number;
        $phoneNumber->type = $type;
        $phoneNumber->status = $status;

        $this->entityManager->persist($phoneNumber);
        $this->entityManager->run();

        return $phoneNumber;
    }
}
