<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\PhoneNumber;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use App\Database\Repository\PhoneNumberRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'phoneNumberService')]
class PhoneNumberService
{
    public function __construct(
        private readonly PhoneNumberRepository $phoneNumberRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation
    ) {}

    /**
     * Create a new phone number
     */
    public function create(array $data): PhoneNumber
    {
        $this->validateCreateData($data);

        $phoneNumber = new PhoneNumber();
        $phoneNumber->number = (int) $data['number'];
        $phoneNumber->type = PhoneNumberType::from($data['type'] ?? PhoneNumberType::VIRTUAL->value);
        $phoneNumber->status = PhoneNumberStatus::from($data['status'] ?? PhoneNumberStatus::ACTIVE->value);
        $phoneNumber->monthlyRate = $data['monthly_rate'] ?? null;

        // Set purchase date to now if monthly rate is provided
        if ($phoneNumber->monthlyRate !== null) {
            $phoneNumber->purchaseDate = new \DateTimeImmutable();
        } else {
            $phoneNumber->purchaseDate = isset($data['purchase_date'])
                ? new \DateTimeImmutable($data['purchase_date'])
                : null;
        }

        $this->entityManager->persist($phoneNumber);
        $this->entityManager->run();

        return $phoneNumber;
    }

    /**
     * Update an existing phone number
     */
    public function update(PhoneNumber $phoneNumber, array $data): PhoneNumber
    {
        $this->validateUpdateData($phoneNumber, $data);

        // Only allow number change if not "purchased" (no monthly rate)
        if (isset($data['number']) && $phoneNumber->monthlyRate === null) {
            $phoneNumber->number = (int) $data['number'];
        }

        if (isset($data['type'])) {
            $phoneNumber->type = PhoneNumberType::from($data['type']);
        }

        if (isset($data['status'])) {
            $phoneNumber->status = PhoneNumberStatus::from($data['status']);
        }

        if (isset($data['monthly_rate'])) {
            $phoneNumber->monthlyRate = $data['monthly_rate'];

            // Set purchase date if monthly rate is being set for the first time
            if ($phoneNumber->purchaseDate === null && $data['monthly_rate'] !== null) {
                $phoneNumber->purchaseDate = new \DateTimeImmutable();
            }
        }

        $this->entityManager->persist($phoneNumber);
        $this->entityManager->run();

        return $phoneNumber;
    }

    /**
     * Check if a phone number is purchased (has monthly rate)
     */
    public function isPurchased(PhoneNumber $phoneNumber): bool
    {
        return $phoneNumber->monthlyRate !== null;
    }

    /**
     * Find phone number by number
     */
    public function findByNumber(int $number): ?PhoneNumber
    {
        return $this->phoneNumberRepository->findByNumber($number);
    }

    /**
     * Get available phone numbers (active status)
     */
    public function getAvailablePhoneNumbers(): array
    {
        return $this->phoneNumberRepository->findByStatus(PhoneNumberStatus::ACTIVE);
    }

    /**
     * Validate create data
     */
    private function validateCreateData(array $data): void
    {
        $rules = [
            'number' => [
                'required',
                'integer',
                ['number::range', 1000000000, 99999999999] // 10-11 digit numbers
            ],
            'type' => [
                'string',
                ['in_array', array_column(PhoneNumberType::cases(), 'value'), true]
            ],
            'status' => [
                'string',
                ['in_array', array_column(PhoneNumberStatus::cases(), 'value'), true]
            ],
            'monthly_rate' => [
                'numeric',
                ['number::higher', 0]
            ],
            'purchase_date' => ['datetime']
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }

        // Check if number already exists
        if ($this->findByNumber((int) $data['number'])) {
            throw new \InvalidArgumentException('Phone number already exists');
        }
    }

    /**
     * Validate update data
     */
    private function validateUpdateData(PhoneNumber $phoneNumber, array $data): void
    {
        $rules = [
            'number' => [
                'integer',
                ['number::range', 1000000000, 99999999999]
            ],
            'type' => [
                'string',
                ['in_array', array_column(PhoneNumberType::cases(), 'value'), true]
            ],
            'status' => [
                'string',
                ['in_array', array_column(PhoneNumberStatus::cases(), 'value'), true]
            ],
            'monthly_rate' => [
                'numeric',
                ['number::higher', 0]
            ]
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }

        // Check if trying to change number on purchased phone
        if (isset($data['number']) && $this->isPurchased($phoneNumber)) {
            throw new \InvalidArgumentException('Cannot change number for purchased phone numbers');
        }

        // Check if new number already exists (if changing number)
        if (isset($data['number']) && $data['number'] !== $phoneNumber->number) {
            if ($this->findByNumber((int) $data['number'])) {
                throw new \InvalidArgumentException('Phone number already exists');
            }
        }
    }
}
