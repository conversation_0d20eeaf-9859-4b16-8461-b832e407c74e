<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Agent;
use App\Database\Entity\ForwardPhoneNumber;
use App\Database\Entity\ForwardSequence;
use App\Database\Entity\PhoneNumber;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'forwardSequenceService')]
class ForwardSequenceService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation,
        private readonly PhoneNumberService $phoneNumberService
    ) {}

    /**
     * Create a new forward sequence for an agent
     */
    public function create(Agent $agent, array $forwardNumbers): ForwardSequence
    {
        $this->validateForwardNumbers($forwardNumbers);

        $forwardSequence = new ForwardSequence();
        $forwardSequence->agent = $agent;

        $this->entityManager->persist($forwardSequence);
        $this->entityManager->run();

        // Add forward phone numbers
        foreach ($forwardNumbers as $index => $forwardData) {
            $this->addForwardPhoneNumber($forwardSequence, $forwardData, $index + 1);
        }

        return $forwardSequence;
    }

    /**
     * Update forward sequence by replacing all forward phone numbers
     */
    public function update(ForwardSequence $forwardSequence, array $forwardNumbers): ForwardSequence
    {
        $this->validateForwardNumbers($forwardNumbers);

        // Remove existing forward phone numbers
        foreach ($forwardSequence->forwardPhoneNumbers as $forwardPhoneNumber) {
            $this->entityManager->delete($forwardPhoneNumber);
        }
        $this->entityManager->run();

        // Add new forward phone numbers
        foreach ($forwardNumbers as $index => $forwardData) {
            $this->addForwardPhoneNumber($forwardSequence, $forwardData, $index + 1);
        }

        return $forwardSequence;
    }

    /**
     * Add a phone number to the forward sequence
     */
    public function addForwardPhoneNumber(ForwardSequence $forwardSequence, array $data, ?int $order = null): ForwardPhoneNumber
    {
        $this->validateForwardPhoneNumberData($data);

        // Get or create phone number
        $phoneNumber = $this->getOrCreatePhoneNumber($data);

        // Determine order
        if ($order === null) {
            $order = $forwardSequence->forwardPhoneNumbers->count() + 1;
        }

        $forwardPhoneNumber = new ForwardPhoneNumber();
        $forwardPhoneNumber->forwardSequence = $forwardSequence;
        $forwardPhoneNumber->phoneNumber = $phoneNumber;
        $forwardPhoneNumber->order = $order;
        $forwardPhoneNumber->waitTime = $data['wait_time'] ?? 30; // Default 30 seconds

        $this->entityManager->persist($forwardPhoneNumber);
        $this->entityManager->run();

        return $forwardPhoneNumber;
    }

    /**
     * Remove a phone number from the forward sequence
     */
    public function removeForwardPhoneNumber(ForwardPhoneNumber $forwardPhoneNumber): void
    {
        $forwardSequence = $forwardPhoneNumber->forwardSequence;
        $removedOrder = $forwardPhoneNumber->order;

        // Delete the forward phone number
        $this->entityManager->delete($forwardPhoneNumber);

        // Reorder remaining phone numbers
        foreach ($forwardSequence->forwardPhoneNumbers as $fpn) {
            if ($fpn->order > $removedOrder) {
                $fpn->order--;
                $this->entityManager->persist($fpn);
            }
        }

        $this->entityManager->run();
    }

    /**
     * Reorder forward phone numbers
     */
    public function reorderForwardPhoneNumbers(ForwardSequence $forwardSequence, array $phoneNumberIds): void
    {
        foreach ($phoneNumberIds as $index => $phoneNumberId) {
            foreach ($forwardSequence->forwardPhoneNumbers as $fpn) {
                if ($fpn->phoneNumber->id->toString() === $phoneNumberId) {
                    $fpn->order = $index + 1;
                    $this->entityManager->persist($fpn);
                    break;
                }
            }
        }

        $this->entityManager->run();
    }

    /**
     * Update wait time for a forward phone number
     */
    public function updateWaitTime(ForwardPhoneNumber $forwardPhoneNumber, int $waitTime): ForwardPhoneNumber
    {
        if ($waitTime < 0 || $waitTime > 300) { // Max 5 minutes
            throw new \InvalidArgumentException('Wait time must be between 0 and 300 seconds');
        }

        $forwardPhoneNumber->waitTime = $waitTime;
        $this->entityManager->persist($forwardPhoneNumber);
        $this->entityManager->run();

        return $forwardPhoneNumber;
    }

    /**
     * Delete forward sequence
     */
    public function delete(ForwardSequence $forwardSequence): void
    {
        // Delete all forward phone numbers first
        foreach ($forwardSequence->forwardPhoneNumbers as $forwardPhoneNumber) {
            $this->entityManager->delete($forwardPhoneNumber);
        }

        $this->entityManager->delete($forwardSequence);
        $this->entityManager->run();
    }

    /**
     * Get or create phone number from data
     */
    private function getOrCreatePhoneNumber(array $data): PhoneNumber
    {
        // If phone_number_id is provided, use existing phone number
        if (isset($data['phone_number_id'])) {
            $phoneNumber = $this->entityManager->getRepository(PhoneNumber::class)->findByPK($data['phone_number_id']);
            if (!$phoneNumber) {
                throw new \InvalidArgumentException('Phone number not found');
            }
            return $phoneNumber;
        }

        // If number is provided, find or create phone number
        if (isset($data['number'])) {
            $existingPhone = $this->phoneNumberService->findByNumber((int) $data['number']);
            if ($existingPhone) {
                return $existingPhone;
            }

            // Create new phone number
            return $this->phoneNumberService->create([
                'number' => $data['number'],
                'type' => $data['type'] ?? 'virtual',
                'status' => $data['status'] ?? 'active'
            ]);
        }

        throw new \InvalidArgumentException('Either phone_number_id or number must be provided');
    }

    /**
     * Validate forward numbers array
     */
    private function validateForwardNumbers(array $forwardNumbers): void
    {
        if (empty($forwardNumbers)) {
            throw new \InvalidArgumentException('At least one forward phone number is required');
        }

        if (count($forwardNumbers) > 10) {
            throw new \InvalidArgumentException('Maximum 10 forward phone numbers allowed');
        }

        foreach ($forwardNumbers as $index => $data) {
            $this->validateForwardPhoneNumberData($data, $index);
        }
    }

    /**
     * Validate forward phone number data
     */
    private function validateForwardPhoneNumberData(array $data, ?int $index = null): void
    {
        $prefix = $index !== null ? "Forward number {$index}: " : '';

        $rules = [
            'wait_time' => [
                'integer',
                ['number::range', 0, 300]
            ],
        ];

        // Either phone_number_id or number must be provided
        if (!isset($data['phone_number_id']) && !isset($data['number'])) {
            throw new \InvalidArgumentException($prefix . 'Either phone_number_id or number must be provided');
        }

        if (isset($data['phone_number_id'])) {
            $rules['phone_number_id'] = [
                'required',
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ];
        }

        if (isset($data['number'])) {
            $rules['number'] = [
                'integer',
                ['number::range', 1000000000, 99999999999]
            ];
            $rules['type'] = [
                'string',
                ['in_array', ['landline', 'virtual', 'mobile', 'fax'], true]
            ];
            $rules['status'] = [
                'string',
                ['in_array', ['active', 'inactive', 'pending'], true]
            ];
        }

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException($prefix . 'Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }
}
