<?php

namespace App\Database\Entity;

use App\Database\Enum\CallStatus;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Call
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Lead::class, innerKey: 'lead_id')]
    public Lead $lead;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id')]
    public Agent $agent;
    #[BelongsTo(target: Conversation::class, innerKey: 'conversation_id', nullable: false)]
    public Conversation $conversation;
    #[Column(type: 'boolean', nullable: false, default: false)]
    public bool $answered = false;
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'answered_by_id', nullable: true)]
    public ?PhoneNumber $answeredBy;
    #[HasOne(target: Voicemail::class)]
    public ?Voicemail $voicemail;
    #[Column(type: 'integer', nullable: false, default: 0)]
    public int $duration = 0;
    #[Column(type: 'float', nullable: false, default: 0.0)]
    public float $cost = 0.0;
    #[Column(type: 'enum(ringing,answered,in_progress,busy,no_answer,failed,cancelled)', nullable: false, default: CallStatus::RINGING->value, typecast: CallStatus::class)]
    public CallStatus $status;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $startTime;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $answerTime;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $endTime;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct(
    ) {}
}
