<?php

namespace App\Database\Entity;

use App\Database\Enum\LeadPriority;
use App\Database\Enum\LeadStatus;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\ManyToMany;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Lead
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Person::class, innerKey: 'person_id')]
    public Person $person;
    #[BelongsTo(target: Organization::class, innerKey: 'organization_id')]
    public Organization $organization;
    #[Column(type: 'string', nullable: true)]
    public ?string $service;
    #[Column(type: 'enum(new,contacted,qualified,converted,lost)', nullable: false, default: LeadStatus::NEW->value, typecast: LeadStatus::class)]
    public LeadStatus $status;
    #[Column(type: 'enum(high,medium,low)', nullable: false, default: LeadPriority::LOW->value, typecast: LeadPriority::class)]
    public LeadPriority $priority;
    #[Column(type: 'float', nullable: true)]
    public ?float $estimatedValue;
    #[Column(type: 'float', nullable: true)]
    public ?float $actualValue;
    #[Column(type: 'float', nullable: true)]
    public ?float $estimatedCost;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $lastContact;
    #[Column(type: 'integer', nullable: true)]
    public ?int $score;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;
    // relationships
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id')]
    public PhoneNumber $phoneNumber;
    #[ManyToMany(target: Note::class, through: LeadNotes::class, collection: 'doctrine')]
    public ArrayCollection $notes;
    #[HasMany(target: Conversation::class)]
    public ArrayCollection $conversations;
    #[HasMany(target: Appointment::class, collection: 'doctrine')]
    public ArrayCollection $appointments;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id' )]
    public Agent $agent;

//    ai analysis
//    ai summary
//    ai sentiment analysis
    public function __construct(
    ) {
        $this->notes = new ArrayCollection();
        $this->conversations = new ArrayCollection();
        $this->appointments = new ArrayCollection();
    }
}
