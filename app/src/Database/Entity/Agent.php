<?php

namespace App\Database\Entity;

use App\Application\Kernel;
use App\Database\Enum\AgentStatus;
use App\Database\Repository\AgentRepository;
use Carbon\Carbon;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\Annotated\Annotation\Relation\ManyToMany;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Ramsey\Uuid\UuidInterface;
use App\Service\CacheService;

#[Entity(repository: AgentRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Agent
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;

    #[Column(type: 'string', nullable: false)]
    public string $name;

    #[Column(type: 'enum(active,inactive,scheduled)', nullable: false, default: AgentStatus::ACTIVE->value, typecast: AgentStatus::class)]
    public AgentStatus $status;

    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;

    #[Column(type: 'datetime', nullable: true)]
    public \DateTimeImmutable $updatedAt;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    // relationships
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id')]
    public PhoneNumber $phoneNumber;

    #[BelongsTo(target: Organization::class, innerKey: 'organization_id')]
    public Organization $organization;

    #[HasMany(target: Conversation::class, orderBy: ['createdAt' => 'ASC'], collection: 'doctrine')]
    public ArrayCollection $conversations;

    #[HasMany(target: Appointment::class, orderBy: ['createdAt' => 'ASC'], collection: 'doctrine')]
    public ArrayCollection $appointments;

    #[ManyToMany(target: Lead::class, through: AgentLeads::class, collection: 'doctrine')]
    public ArrayCollection $leads;

    #[HasOne(target: ForwardSequence::class)]
    public ?ForwardSequence $forwardSequence;

    #[HasOne(target: AutoResponder::class)]
    public ?AutoResponder $autoResponder;

    public function __construct()
    {
        $this->conversations = new ArrayCollection();
        $this->leads = new ArrayCollection();
        $this->appointments = new ArrayCollection();
        die('agent');
    }

    public function calculateLeadResponseRate(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':leadResponseRate',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                return $this->conversations->reduce(function ($sum, $conversation) {
                        return $sum + ($conversation->messages->count() > 1 ? 1 : 0);
                    }, 0) / $this->conversations->count() * 100;
            }
        );
    }

    public function calculateAvgLeadResponseTime(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':avgLeadResponseTime',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                return $this->conversations->reduce(function ($sum, $conversation) {
                        return $sum + $conversation->messages->reduce(function ($sum, $message) use ($conversation) {
                                return $sum + ($message->createdAt->getTimestamp() - $conversation->createdAt->getTimestamp());
                            }, 0);
                    }, 0) / $this->conversations->count();
            }
        );
    }

    public function calculateCallsHandled(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':callsHandled',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $item) {
                    return $sum + $item->calls->count();
                }, 0);
            }
        );
    }

    public function calculateVoicemailCount(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':voicemailCount',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $item) {
                    return $sum + $item->calls->reduce(function ($sum, $call) {
                            return $sum + ($call->voicemail ? 1 : 0);
                        }, 0);
                }, 0);
            }
        );
    }

    public function calculateConversationCount(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':conversationCount',
            calculation: function () {
                return $this->conversations->count();
            }
        );
    }

    public function calculateConversionCount(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':conversionCount',
            calculation: function () {
                return $this->appointments->count();
            }
        );
    }

    public function calculateConversionRate(): float
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':conversionRate',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                if ($this->appointments->isEmpty()) {
                    return 0;
                }
                return $this->appointments->count() / $this->conversations->count() * 100;
            }
        );
    }

    public function calculateAvgConversionTime(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':avgConversionTime',
            calculation: function () {
                if ($this->appointments->isEmpty()) {
                    return 0;
                }
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                return $this->appointments->reduce(function ($sum, $item) {
                        if (empty($item->conversation)) {
                            return $sum;
                        }
                        return $sum + ($item->createdAt->getTimestamp() - $item->conversation->createdAt->getTimestamp());
                    }, 0) / $this->calculateConversionCount();
            }
        );
    }

    public function calculateAvgLeadSentiment(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':avgLeadSentiment',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                return $this->conversations->reduce(function ($sum, $item) {
                        return $sum + random_int(80, 100);
                    }, 0) / $this->conversations->count();
            }
        );
    }

    public function calculateTotalMonthlyCost(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':totalMonthlyCost',
            calculation: function () {
                $phoneNumberCost = $this->phoneNumber->monthlyRate ?? 2.99;
                $messageCost = $this->conversations->reduce(function ($sum, $item) {
                    return $sum + $item->messages->reduce(function ($sum, $message) {
                            return $sum + (0.0082 * 2);
                        }, 0);
                }, 0);
                return $phoneNumberCost + $messageCost;
            }
        );
    }

    public function calculateAverageCostPerConversion(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':averageCostPerConversion',
            calculation: function () {
                if ($this->appointments->isEmpty()) {
                    return 0;
                }
                return $this->calculateTotalMonthlyCost() / $this->calculateConversionCount();
            }
        );
    }

    public function calculateMessageCount(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':messageCount',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $item) {
                    return $sum + $item->messages->count();
                }, 0);
            }
        );
    }

    public function calculateAvgMessagePerConversation(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':avgMessagePerConversation',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return 0;
                }
                if ($this->calculateConversationCount() === 0) {
                    return 0;
                }
                return $this->calculateMessageCount() / $this->calculateConversationCount();
            }
        );
    }

    public function calculateSmsReputation(): int
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':smsReputation',
            calculation: function () {
                // @todo use real sms reputation
                return random_int(80, 99);
            }
        );
    }

    public function calculateLastActivity(): array
    {
        return $this->augmentCache(
            key: 'agent:' . $this->id . ':lastActive',
            calculation: function () {
                if ($this->conversations->isEmpty()) {

                    return [
                        'raw' => null,
                        'formatted' => 'Never',
                    ];

                }
                return [
                    'raw' => $this->conversations->last()->messages->last()->createdAt,
                    'formatted' => Carbon::createFromImmutable($this->conversations->last()->messages->last()->createdAt)->diffForHumans(),
                ];
            }
        );
    }

    private function augmentCache($key, $calculation, $ttl = 60)
    {
        return Kernel::cache()->augmentCache($key, $calculation, $ttl);
    }
}
