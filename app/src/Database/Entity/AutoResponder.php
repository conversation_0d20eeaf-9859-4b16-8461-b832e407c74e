<?php

namespace App\Database\Entity;

use App\Database\Enum\AutoResponderDelay;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class AutoResponder
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'boolean', nullable: false, default: false)]
    public bool $enabled;
    #[Column(type: 'string', nullable: false)]
    public string $toneSample;
    #[Column(type: 'enum(0,1-30,30-60,60-300,300-600,600-1800,1800-3600,random)', nullable: false, default: AutoResponderDelay::NONE->value, typecast: AutoResponderDelay::class)]
    public AutoResponderDelay $responseDelay;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id')]
    public Agent $agent;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct(
    ) {}
}
