<?php

declare(strict_types=1);

namespace App\Database\Entity;

use App\Database\Repository\UserRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: UserRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class User
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'string')]
    public string $username;
    #[Column(type: 'string', nullable: true)]
    public string $password;
    #[Column(type: 'boolean', nullable: false, default: false)]
    public bool $verified = false;
    #[Column(type: 'datetime')]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;
    // relationships
    #[BelongsTo(target: Person::class, innerKey: 'person_id')]
    public Person $person;
    #[BelongsTo(target: Organization::class, innerKey: 'organization_id', nullable: true)]
    public ?Organization $organization;

    public function __construct(
    ) {}
}
