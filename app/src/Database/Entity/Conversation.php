<?php

namespace App\Database\Entity;

use App\Database\Enum\ConversationPriority;
use App\Database\Enum\ConversationStatus;
use App\Database\Repository\ConversationRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\ManyToMany;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: ConversationRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Conversation
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Lead::class, innerKey: 'lead_id')]
    public Lead $lead;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id')]
    public Agent $agent;
    #[Column(type: 'enum(active,pending,resolved,archived)', nullable: false, default: ConversationStatus::ACTIVE->value, typecast: ConversationStatus::class)]
    public ConversationStatus $status;
    #[Column(type: 'enum(high,medium,low)', nullable: false, default: ConversationPriority::LOW->value, typecast: ConversationPriority::class)]
    public ConversationPriority $priority;
    #[Column(type: 'boolean', nullable: false, default: true)]
    public bool $isAgentActive = true;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    // relationships
    #[HasMany(target: Call::class, orderBy: ['createdAt' => 'ASC'], collection: 'doctrine')]
    public ArrayCollection $calls;
    #[HasMany(target: Message::class, orderBy: ['createdAt' => 'ASC'], collection: 'doctrine')]
    public ArrayCollection $messages;
    #[ManyToMany(target: Note::class, through: ConversationNotes::class, collection: 'doctrine')]
    public ArrayCollection $notes;

    public function __construct(
    ) {
        $this->calls = new ArrayCollection();
        $this->messages = new ArrayCollection();
        $this->notes = new ArrayCollection();
    }
}
