<?php

namespace App\Database\Entity;

use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Note
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'string', nullable: false)]
    public string $content;
    #[BelongsTo(target: User::class, innerKey: 'author_id', nullable: true)]
    public User $author;
    #[Column(type: 'datetime')]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public \DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt;

    public function __construct(
    ) {}
}
