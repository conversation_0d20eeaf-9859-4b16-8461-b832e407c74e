<?php

namespace App\Database\Entity;

use App\Database\Enum\MessageSender;
use App\Database\Repository\MessageRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: MessageRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Message
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Conversation::class, innerKey: 'conversation_id')]
    public Conversation $conversation;
    #[Column(type: 'string', nullable: false)]
    public string $content;
    #[Column(type: 'enum(lead,agent,user)', nullable: false, default: MessageSender::LEAD->value, typecast: MessageSender::class)]
    public MessageSender $sender;
    #[BelongsTo(target: User::class, innerKey: 'author_id', nullable: true)]
    public ?User $author;
    #[BelongsTo(target: Lead::class, innerKey: 'lead_id', nullable: true)]
    public ?Lead $lead;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id', nullable: true)]
    public ?Agent $agent;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct(
    ) {}
}
