<?php

namespace App\Database\Entity;

use App\Database\Repository\AddressRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use <PERSON>\Uuid\UuidInterface;

#[Entity(repository: AddressRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Address
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'string', nullable: false)]
    public string $street1;
    #[Column(type: 'string', nullable: true)]
    public ?string $street2;
    #[Column(type: 'string', nullable: true)]
    public ?string $street3;
    #[Column(type: 'string', nullable: true)]
    public ?string $street4;
    #[Column(type: 'string', nullable: false)]
    public string $city;
    #[Column(type: 'string', nullable: false)]
    public string $state;
    #[Column(type: 'string', nullable: false)]
    public string $zip;
    #[Column(type: 'datetime')]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct(
    ) {}
}
