<?php

namespace App\Database\Entity;

use App\Database\Repository\PersonRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: PersonRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Person
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;

    #[Column(type: 'string', nullable: true)]
    public ?string $firstName;

    #[Column(type: 'string', nullable: true)]
    public ?string $lastName;

    #[Column(type: 'string', nullable: true)]
    public ?string $email;

    #[Column(type: 'datetime')]
    public \DateTimeImmutable $createdAt;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id', nullable: true)]
    public ?PhoneNumber $phoneNumber;

    #[BelongsTo(target: Address::class, innerKey: 'author_id', nullable: true)]
    public ?Address $address;

    #[HasOne(target: User::class, nullable: true)]
    public ?User $user;

    #[HasMany(target: Lead::class, nullable: true, collection: 'doctrine')]
    public ?ArrayCollection $leads;

    public function __construct(
    ) {
        $this->leads = new ArrayCollection();
    }
}
