<?php

namespace App\Database\Entity;

use App\Database\Enum\AppointmentStatus;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\ManyToMany;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Appointment
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Lead::class, innerKey: 'lead_id')]
    public Lead $lead;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id')]
    public Agent $agent;
    #[BelongsTo(target: Conversation::class, innerKey: 'conversation_id', nullable: true)]
    public ?Conversation $conversation;
    #[Column(type: 'string', nullable: false)]
    public string $service;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $startTime;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $endTime;
    #[Column(type: 'float', nullable: false)]
    public float $value;
    #[ManyToMany(target: Note::class, through: AppointmentNotes::class, collection: 'doctrine')]
    public ArrayCollection $notes;
    #[Column(type: 'enum(confirmed,pending,rescheduled,cancelled)', nullable: false, default: AppointmentStatus::PENDING->value, typecast: AppointmentStatus::class)]
    public AppointmentStatus $status;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;


    public function __construct(
    ) {
        $this->notes = new ArrayCollection();
    }
}
