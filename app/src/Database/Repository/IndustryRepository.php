<?php

declare(strict_types=1);

namespace App\Database\Repository;

use App\Database\Entity\Industry;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'industries')]
class IndustryRepository extends Repository
{
    /**
     * Find industry by name
     */
    public function findByName(string $name): ?Industry
    {
        return $this->findOne(['name' => $name]);
    }

    /**
     * Get all active industries
     */
    public function getActiveIndustries(): array
    {
        return $this->select()
            ->where('deletedAt', null)
            ->orderBy('name', 'ASC')
            ->fetchAll();
    }
}
