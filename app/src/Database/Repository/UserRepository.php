<?php

declare(strict_types=1);

namespace App\Database\Repository;

use App\Database\Entity\User;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'users')]
class UserRepository extends Repository
{
    /**
     * Find user by email through person relationship
     */
    public function findByEmail(string $email): ?User
    {
        return $this->select()
            ->with('person')
            ->where('person.email', $email)
            ->fetchOne();
    }

    /**
     * Find user by username or email
     */
    public function findByUsernameOrEmail(string $identifier): ?User
    {
        // First try username
        $user = $this->findOne(['username' => $identifier]);

        // If not found, try email
        if (!$user) {
            $user = $this->findByEmail($identifier);
        }

        return $user;
    }
}
