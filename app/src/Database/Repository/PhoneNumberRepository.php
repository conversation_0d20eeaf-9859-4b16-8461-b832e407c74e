<?php

declare(strict_types=1);

namespace App\Database\Repository;

use App\Database\Entity\PhoneNumber;
use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use Cycle\ORM\Select\Repository;
use <PERSON><PERSON><PERSON>\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'phoneNumbers')]
class PhoneNumberRepository extends Repository
{
    /**
     * Find phone number by number
     */
    public function findByNumber(int $number): ?PhoneNumber
    {
        return $this->findOne(['number' => $number]);
    }

    /**
     * Find available phone numbers
     */
    public function findAvailable(): array
    {
        return $this->select()
            ->where('status', PhoneNumberStatus::ACTIVE)
            ->where('deletedAt', null)
            ->fetchAll();
    }

    /**
     * Find phone numbers by type
     */
    public function findByType(PhoneNumberType $type): array
    {
        return $this->select()
            ->where('type', $type)
            ->where('deletedAt', null)
            ->fetchAll();
    }

    /**
     * Find phone numbers by status
     */
    public function findByStatus(PhoneNumberStatus $status): array
    {
        return $this->select()
            ->where('status', $status)
            ->where('deletedAt', null)
            ->fetchAll();
    }
}
