<?php

namespace App\Database\Repository;

use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'agents')]
class AgentRepository extends Repository
{
    public function getAgentWithLeads(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('leads')->fetchOne();
    }

    public function getAgentWithConversations(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('conversations')->fetchOne();
    }

    public function getAgentWithAutoResponder(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('autoResponder')->fetchOne();
    }

    public function getAgentWithForwardSequence(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('forwardSequence')->fetchOne();
    }

    public function getAgentWithPhoneNumber(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('phoneNumber')->fetchOne();
    }

    public function getAgentWithOrganization(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->with('organization')->fetchOne();
    }

    public function getAgentWithAll(string $agentId)
    {
        return $this->select()->with([
            'leads',
            'conversations',
            'autoResponder',
            'forwardSequence',
            'phoneNumber',
            'organization',
        ])->fetchOne();
    }
}
