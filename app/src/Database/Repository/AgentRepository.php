<?php

namespace App\Database\Repository;

use Cycle\ORM\Select;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'agents')]
class AgentRepository extends Repository
{
    public function getAgentWithLeads(string $agentId): Select
    {
        return $this->select()->where('id', $agentId)->load('leads');
    }

    public function getAgentWithConversations(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->load('conversations')->fetchOne();
    }

    public function getAgentWithAutoResponder(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->load('autoResponder')->fetchOne();
    }

    public function getAgentWithForwardSequence(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->load('forwardSequence')->fetchOne();
    }

    public function getAgentWithPhoneNumber(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->load('phoneNumber')->fetchOne();
    }

    public function getAgentWithOrganization(string $agentId): ?Agent
    {
        return $this->select()->where('id', $agentId)->load('organization')->fetchOne();
    }

    public function getAgentWithAll(string $agentId)
    {
        return $this->select()->load([
            'leads',
            'conversations',
            'autoResponder',
            'forwardSequence',
            'phoneNumber',
            'organization',
        ])->fetchOne();
    }
}
