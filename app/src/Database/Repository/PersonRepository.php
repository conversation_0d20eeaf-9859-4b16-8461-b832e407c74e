<?php

declare(strict_types=1);

namespace App\Database\Repository;

use App\Database\Entity\Person;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'persons')]
class PersonRepository extends Repository
{
    /**
     * Find person by email address
     */
    public function findByEmail(string $email): ?Person
    {
        return $this->findOne(['email' => $email]);
    }

    /**
     * Find persons by name (first name or last name)
     */
    public function findByName(string $name): array
    {
        return $this->select()
            ->where('firstName', 'LIKE', "%{$name}%")
            ->orWhere('lastName', 'LIKE', "%{$name}%")
            ->fetchAll();
    }

    /**
     * Find person with user relationship
     */
    public function findWithUser(string $personId): ?Person
    {
        return $this->select()
            ->with('user')
            ->where('id', $personId)
            ->fetchOne();
    }
}
