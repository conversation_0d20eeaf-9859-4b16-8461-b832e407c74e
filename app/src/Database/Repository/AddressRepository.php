<?php

declare(strict_types=1);

namespace App\Database\Repository;

use App\Database\Entity\Address;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'addresses')]
class AddressRepository extends Repository
{
    /**
     * Find addresses by city and state
     */
    public function findByCityAndState(string $city, string $state): array
    {
        return $this->select()
            ->where('city', $city)
            ->where('state', $state)
            ->where('deletedAt', null)
            ->fetchAll();
    }

    /**
     * Find addresses by zip code
     */
    public function findByZipCode(string $zipCode): array
    {
        return $this->select()
            ->where('zip', $zipCode)
            ->where('deletedAt', null)
            ->fetchAll();
    }
}
