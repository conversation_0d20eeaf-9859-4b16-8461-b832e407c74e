{"name": "spiral/app", "type": "project", "license": "MIT", "description": "Spiral Application installer", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/app/issues", "source": "https://github.com/spiral/app"}, "require": {"php": ">=8.1", "ext-mbstring": "*", "ext-sockets": "*", "cycle/entity-behavior": "^1.7", "cycle/entity-behavior-uuid": "^1.2", "doctrine/collections": "^2.3", "firebase/php-jwt": "^6.11", "spiral-packages/league-event": "^1.0.1", "spiral-packages/scheduler": "^2.1", "spiral-packages/yii-error-handler-bridge": "^1.1", "spiral/cycle-bridge": "^2.11", "spiral/data-grid-bridge": "^3.0.1", "spiral/framework": "^3.15.7", "spiral/http": "^3.15", "spiral/nyholm-bridge": "^1.3", "spiral/roadrunner-bridge": "^4.0", "spiral/roadrunner-cli": "^2.5", "spiral/temporal-bridge": "^3.3", "spiral/validator": "^1.5", "spiral/views": "^3.15"}, "require-dev": {"jzonta/faker-restaurant": "^2.0", "spiral-packages/database-seeder": "^3.3", "spiral/code-style": "^2.2", "spiral/dumper": "^3.3.1", "spiral/testing": "^2.3", "vimeo/psalm": "^6.10"}, "autoload": {"psr-4": {"App\\": "app/src", "Database\\": "app/database"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests"}}, "extra": {"publish-cmd": "php app.php publish"}, "config": {"sort-packages": true, "allow-plugins": {"spiral/composer-publish-plugin": true}}, "scripts": {"post-create-project-cmd": ["php app.php encrypt:key -m .env", "php app.php configure --quiet", "rr get-binary --quiet", "composer dump-autoload"], "rr:download": "rr get-binary", "rr:download-protoc": "rr download-protoc-binary", "cs:fix": "php-cs-fixer fix -v", "psalm": "psalm", "psalm:baseline": "psalm --set-baseline=psalm-baseline.xml", "test": "phpunit", "test-coverage": "phpunit --coverage"}, "minimum-stability": "dev", "prefer-stable": true}